# 🎬 INTELLIGENT ASSET DEPENDENCY SYSTEM

## Overview

The **Intelligent Asset Dependency System** automatically detects, analyzes, and resolves external file dependencies in Lottie JSON animations. When a Lottie file references external images, videos, audio files, or fonts, this system provides an **interactive GUI** to help you locate and link the missing files.

## 🎯 Key Features

### ✅ **Automatic Dependency Detection**
- **Images**: PNG, JPG, GIF, WebP, etc.
- **Videos**: MP4, MOV, AVI, WebM, etc.
- **Audio**: MP3, WAV, AAC, OGG, etc.
- **Fonts**: TTF, OTF, WOFF, etc.
- **Embedded Assets**: Base64-encoded images (no file needed)
- **Precomps**: Nested compositions (handled internally)

### ✅ **Interactive File Resolution**
- **Smart File Browser**: Opens file dialogs with appropriate filters
- **Drag & Drop Support**: Modern GUI interface
- **Path Validation**: Checks if selected files exist
- **Error Handling**: Graceful fallbacks for missing files

### ✅ **Intelligent JSX Generation**
- **Asset Embedding**: Generates proper After Effects import code
- **Error Recovery**: Creates placeholder layers for missing assets
- **Path Management**: Handles absolute and relative file paths
- **Import Optimization**: Efficient asset loading in After Effects

## 🔧 How It Works

### 1. **Dependency Analysis Phase**
```python
# The system analyzes the JSON structure
dependencies = asset_manager.analyze_dependencies('animation.json')
```

**What it detects:**
- Asset references in the `assets` array
- Font references in the `fonts` section
- Layer references to external files
- Embedded vs. external asset classification

### 2. **Interactive Resolution Phase**
```python
# Opens GUI for missing assets
resolved_deps = asset_manager.interactive_asset_resolution(dependencies)
```

**GUI Features:**
- **Asset Type Grouping**: Images, videos, audio, fonts
- **File Information**: Shows original paths and asset IDs
- **Browse Buttons**: Opens appropriate file dialogs
- **Real-time Validation**: Checks file existence
- **Batch Resolution**: Handle multiple assets at once

### 3. **JSX Integration Phase**
```python
# Generates JSX with proper asset imports
jsx_code = converter.generate_jsx_script(lottie_data)
```

**Generated Code:**
```javascript
// For resolved assets
var layer_1 = createAssetReference(comp, 'image_1', 'C:/path/to/image.png');

// For missing assets
var layer_2 = createNullLayer(comp, 'missing_asset (Missing Image)');

// For embedded assets
var layer_3 = embedImageAsset(comp, 'embedded_1', base64Data, 512, 512);
```

## 🎮 Usage Examples

### **Basic Usage**
```python
from lottie_to_jsx_converter import LottieToJSXConverter

converter = LottieToJSXConverter()
jsx_script = converter.convert_json_to_jsx('animation.json', 'output.jsx')
```

### **Asset Analysis Only**
```python
from asset_dependency_manager import AssetDependencyManager

manager = AssetDependencyManager()
dependencies = manager.analyze_dependencies('animation.json')
print(manager.generate_asset_report(dependencies))
```

### **Interactive Demo**
```bash
python intelligent_converter_demo.py
```

## 📊 Asset Types Supported

| Asset Type | Lottie Property | File Extensions | Auto-Detection |
|------------|----------------|-----------------|----------------|
| **Images** | `"e": 0` | PNG, JPG, GIF, WebP, BMP, TIFF | ✅ |
| **Videos** | `"e": 1` | MP4, MOV, AVI, WebM, MKV, FLV | ✅ |
| **Audio** | `"t": 2` | MP3, WAV, AAC, OGG, FLAC, M4A | ✅ |
| **Fonts** | `fonts.list[]` | TTF, OTF, WOFF, WOFF2 | ✅ |
| **Embedded** | `"p": "data:"` | Base64 encoded | ✅ |
| **Precomps** | `"layers": []` | Internal compositions | ✅ |

## 🎨 GUI Interface

### **Main Window**
- **Title**: "🎬 Asset Dependency Resolver"
- **Dark Theme**: Professional appearance
- **Scrollable**: Handles many assets
- **Responsive**: Adapts to content

### **Asset Widgets**
- **Type Headers**: Grouped by asset type
- **Asset Info**: ID, name, original path
- **File Input**: Text field showing selected path
- **Browse Button**: Opens file dialog with filters
- **Status Indicators**: Visual feedback

### **Action Buttons**
- **✅ Resolve All**: Apply all selections
- **⏭️ Skip Missing**: Continue without resolving
- **❌ Cancel**: Abort conversion

## 🔍 Error Handling

### **Missing Files**
```javascript
// Creates placeholder with warning
var layer = createNullLayer(comp, 'asset_name (Missing File)');
// Shows alert in After Effects
alert('Asset file not found: /path/to/file.png');
```

### **Import Errors**
```javascript
// Graceful fallback
try {
    var footage = app.project.importFile(importOptions);
} catch (e) {
    alert('Error importing asset: ' + e.toString());
    return createNullLayer(comp, 'asset_name (Import Error)');
}
```

### **Path Issues**
- **Relative Paths**: Resolved relative to JSON file location
- **Network Paths**: Supported for shared assets
- **Special Characters**: Properly escaped in JSX
- **Long Paths**: Handled correctly on Windows

## 📈 Benefits

### **For Animators**
- **No Manual Linking**: Automatic asset detection
- **Visual Interface**: Easy file selection
- **Error Prevention**: Validates files before conversion
- **Time Saving**: Batch resolution of multiple assets

### **For Developers**
- **Clean JSX Code**: Proper After Effects scripting
- **Error Recovery**: Graceful handling of missing files
- **Extensible**: Easy to add new asset types
- **Maintainable**: Clear separation of concerns

### **For Teams**
- **Asset Management**: Track dependencies across projects
- **Path Flexibility**: Handle different folder structures
- **Documentation**: Detailed asset reports
- **Consistency**: Standardized asset handling

## 🚀 Advanced Features

### **Batch Processing**
```python
# Process multiple files
for json_file in json_files:
    converter.convert_json_to_jsx(json_file, f"{json_file}.jsx")
```

### **Custom Asset Handlers**
```python
# Extend for new asset types
class CustomAssetManager(AssetDependencyManager):
    def _analyze_asset(self, asset):
        # Custom asset detection logic
        pass
```

### **Automated Workflows**
```python
# Integrate with build systems
def automated_conversion(source_dir, output_dir):
    for lottie_file in find_lottie_files(source_dir):
        convert_with_assets(lottie_file, output_dir)
```

## 🎯 Best Practices

1. **Organize Assets**: Keep assets in predictable folder structures
2. **Use Relative Paths**: Makes projects more portable
3. **Test Conversions**: Always test generated JSX in After Effects
4. **Backup Originals**: Keep original Lottie files safe
5. **Document Dependencies**: Use the asset report feature

## 🔧 Troubleshooting

### **GUI Not Opening**
- Check tkinter installation: `pip install tkinter`
- Verify display settings on headless systems
- Use asset analysis mode for servers

### **File Not Found Errors**
- Verify file paths are correct
- Check file permissions
- Ensure files aren't locked by other applications

### **Import Failures in After Effects**
- Check file format compatibility
- Verify After Effects version supports the format
- Try converting assets to standard formats

---

**🎉 The Intelligent Asset Dependency System makes Lottie-to-JSX conversion seamless and error-free!**

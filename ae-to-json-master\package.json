{"name": "ae-to-json", "version": "1.1.2", "description": "will export an After Effects project as a JSON object", "main": "index.js", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/mikkoh"}, "dependencies": {"JSON2": "^0.1.0", "deep-extend": "^0.4.1", "es5-shim": "^4.5.7", "xtend": "^4.0.1"}, "devDependencies": {"after-effects": "^0.3.2", "babel-cli": "^6.8.0", "babel-polyfill": "^6.8.0", "babel-preset-es2015": "^6.6.0", "big-object-diff": "^0.7.0", "browserify": "^13.0.0", "chalk": "^1.1.3", "deep-equal": "^1.0.1", "nodemon": "^1.9.1", "opn-cli": "^3.1.0", "tape": "^4.5.1", "traverse": "^0.6.6", "uglify-js": "^2.6.2"}, "scripts": {"start": "npm run watch", "test": "npm run open-ae; npm run test-build-run; npm run clean-root;", "prepublish": "npm run compile-ae", "postpublish": "npm run clean-root", "dist": "npm run compile-dist;", "watch": "npm run open-ae; nodemon -w src/ -w test/ -x npm -- run test-build-run-watch", "test-build-run-watch": "npm run test-build-run; npm run clean-root;", "test-build-run": "npm run test-build; node test/", "test-build": "npm run compile-ae;", "transpile": "babel -d ./ src/ --ignore 'src/template/,src/dist.js'", "compile-ae": "npm run transpile; npm run after-effects;", "after-effects": "npm run after-effects-header; npm run after-effects-browserify; npm run after-effects-cat-footer", "after-effects-header": "cat src/template/header.js > after-effects.js", "after-effects-browserify": "browserify index.js -s aeToJSON >> after-effects.js", "after-effects-cat-footer": "cat src/template/footer.js >> after-effects.js", "compile-dist": "npm run transpile; cp src/dist.js dist.js; browserify dist.js -o dist/index.js; npm run clean-root", "clean-root": "rm *.js; rm -rf util/", "export-example": "npm run open-ae; npm run test-build; node example/exportExample.js", "open-ae": "opn test/ae/testProject.aepx"}, "keywords": ["after,effects,json,jsx"], "repository": {"type": "git", "url": "git://github.com/jam3/ae-to-json.git"}, "homepage": "https://github.com/jam3/ae-to-json", "bugs": {"url": "https://github.com/jam3/ae-to-json/issues"}}
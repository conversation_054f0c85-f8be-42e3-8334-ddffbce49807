var getValuesFromTree = require('./getValuesFromTree');
var checkValueForPath = require('./checkValueForPath');

module.exports = function(t) {
  var json = global.jsonFromAE;
  var values = getValuesFromTree(global.jsonFromAE, 'propertyType');
  var expectedValues = getExpectedValues();

  Object.keys(expectedValues).forEach(function(path) {
    checkValueForPath(t, json, path, expectedValues[ path ]);
  });
  
  t.end();
};




function getExpectedValues() {
  return {
    "project|items|1|layers|0|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Marker|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|0|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|0|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|0|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|0|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|1|layers|0|properties|Audio|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Marker|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|1|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|1|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|1|layers|1|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Rotation|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|1|layers|1|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|1|layers|1|properties|Audio|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Marker|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|0|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|0|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|0|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Z Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|0|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|3|layers|0|properties|Audio|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Marker|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|1|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|1|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|1|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Z Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|1|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|3|layers|1|properties|Audio|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Marker|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|2|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|2|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|2|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Z Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|2|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|3|layers|2|properties|Audio|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Marker|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Time Remap|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Motion Trackers|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|3|properties|Masks|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|3|properties|Effects|propertyType": "INDEXED_GROUP",
    "project|items|3|layers|3|properties|Transform|Anchor Point|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Position|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|X Position|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Y Position|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Z Position|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Orientation|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|X Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Y Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Rotation|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Transform|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Global Light Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Global Light Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Blending Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Spread|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Outer Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Noise|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Color Type|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Source|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Choke|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Range|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Jitter|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Inner Glow|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Style|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Technique|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Depth|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Direction|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Soften|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Altitude|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Distance|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|Invert|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Satin|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Color Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Colors|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Angle|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Style|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Reverse|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Scale|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Offset|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|Blend Mode|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|Color|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|Size|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|Opacity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|Position|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Layer Styles|Stroke|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Layer Styles|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Geometry Options|Bevel Style|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Geometry Options|Bevel Direction|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Geometry Options|Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Geometry Options|Hole Bevel Depth|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Geometry Options|Extrusion Depth|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Geometry Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Material Options|Casts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Light Transmission|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Accepts Shadows|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Accepts Lights|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Appears in Reflections|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Ambient|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Diffuse|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Specular Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Specular Shininess|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Metal|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Reflection Intensity|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Reflection Sharpness|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Reflection Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Transparency|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Transparency Rolloff|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|Index of Refraction|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Material Options|propertyType": "NAMED_GROUP",
    "project|items|3|layers|3|properties|Audio|Audio Levels|propertyType": "PROPERTY",
    "project|items|3|layers|3|properties|Audio|propertyType": "NAMED_GROUP"
  };
}
var getValuesFromTree = require('./getValuesFromTree');
var checkValueForPath = require('./checkValueForPath');

module.exports = function(t) {
  var json = global.jsonFromAE;
  var values = getValuesFromTree(global.jsonFromAE, 'propertyValueType');
  var expectedValues = getExpectedValues();

  // console.log(JSON.stringify(values, null, '  '));

  Object.keys(expectedValues).forEach(function(path) {
    checkValueForPath(t, json, path, expectedValues[ path ]);
  });
  
  t.end();
};




function getExpectedValues() {
  return {
      "project|items|1|layers|0|properties|Marker|propertyValueType": "MARKER",
      "project|items|1|layers|0|properties|Time Remap|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|0|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|0|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|1|layers|0|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|0|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Rotation|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|1|layers|0|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|1|layers|0|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|1|layers|0|properties|Audio|Audio Levels|propertyValueType": "TwoD",
      "project|items|1|layers|1|properties|Marker|propertyValueType": "MARKER",
      "project|items|1|layers|1|properties|Time Remap|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|1|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|1|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|1|layers|1|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|1|layers|1|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Rotation|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|1|layers|1|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|1|layers|1|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|1|layers|1|properties|Audio|Audio Levels|propertyValueType": "TwoD",
      "project|items|3|layers|0|properties|Marker|propertyValueType": "MARKER",
      "project|items|3|layers|0|properties|Time Remap|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|0|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|0|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|3|layers|0|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|0|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Z Rotation|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|0|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|3|layers|0|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|3|layers|0|properties|Audio|Audio Levels|propertyValueType": "TwoD",
      "project|items|3|layers|1|properties|Marker|propertyValueType": "MARKER",
      "project|items|3|layers|1|properties|Time Remap|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|1|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|1|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|3|layers|1|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|1|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Z Rotation|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|1|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|3|layers|1|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|3|layers|1|properties|Audio|Audio Levels|propertyValueType": "TwoD",
      "project|items|3|layers|2|properties|Marker|propertyValueType": "MARKER",
      "project|items|3|layers|2|properties|Time Remap|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|2|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|2|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|3|layers|2|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|2|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Z Rotation|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|2|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|3|layers|2|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|3|layers|2|properties|Audio|Audio Levels|propertyValueType": "TwoD",
      "project|items|3|layers|3|properties|Marker|propertyValueType": "MARKER",
      "project|items|3|layers|3|properties|Time Remap|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Anchor Point|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|3|properties|Transform|Position|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|3|properties|Transform|X Position|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Y Position|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Z Position|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Scale|propertyValueType": "ThreeD",
      "project|items|3|layers|3|properties|Transform|Orientation|propertyValueType": "ThreeD_SPATIAL",
      "project|items|3|layers|3|properties|Transform|X Rotation|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Y Rotation|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Rotation|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Transform|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Global Light Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Global Light Altitude|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Fill Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Red|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Green|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Blue|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Blend Interior Styles as Group|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Blending Options|Advanced Blending|Use Blend Ranges from Source|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Drop Shadow|Layer Knocks Out Drop Shadow|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Distance|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Shadow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Spread|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Outer Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Noise|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Color Type|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Technique|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Source|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Choke|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Range|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Inner Glow|Jitter|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Style|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Technique|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Depth|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Direction|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Soften|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Use Global Light|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Altitude|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Highlight Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Bevel and Emboss|Shadow Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Distance|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Satin|Invert|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Color Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Colors|propertyValueType": "NO_VALUE",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Gradient Smoothness|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Angle|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Style|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Reverse|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Align with Layer|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Gradient Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Link with Layer|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Scale|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Pattern Overlay|Offset|propertyValueType": "TwoD_SPATIAL",
      "project|items|3|layers|3|properties|Layer Styles|Stroke|Blend Mode|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Stroke|Color|propertyValueType": "COLOR",
      "project|items|3|layers|3|properties|Layer Styles|Stroke|Size|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Stroke|Opacity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Layer Styles|Stroke|Position|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Geometry Options|Bevel Style|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Geometry Options|Bevel Direction|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Geometry Options|Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Geometry Options|Hole Bevel Depth|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Geometry Options|Extrusion Depth|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Casts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Light Transmission|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Accepts Shadows|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Accepts Lights|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Appears in Reflections|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Ambient|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Diffuse|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Specular Intensity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Specular Shininess|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Metal|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Reflection Intensity|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Reflection Sharpness|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Reflection Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Transparency|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Transparency Rolloff|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Material Options|Index of Refraction|propertyValueType": "OneD",
      "project|items|3|layers|3|properties|Audio|Audio Levels|propertyValueType": "TwoD"
    };
}
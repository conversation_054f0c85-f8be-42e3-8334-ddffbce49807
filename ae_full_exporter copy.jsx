// Enhanced After Effects to JSON Exporter
// Based on ae-to-json but with custom effect handling
// This script exports ALL layer properties including built-in effects

function exportFullAEProject() {
    var project = app.project;
    var exportData = {
        project: {
            name: project.file ? project.file.name : "Untitled",
            items: []
        },
        timestamp: new Date().toString(),
        version: "1.0.0"
    };

    // Export all project items
    for (var i = 1; i <= project.items.length; i++) {
        var item = project.items[i];
        exportData.project.items.push(exportItem(item));
    }

    return exportData;
}

function exportItem(item) {
    var itemData = {
        name: item.name,
        typeName: item.typeName,
        id: item.id
    };

    // Use typeName instead of instanceof for ExtendScript compatibility
    if (item.typeName === "Composition") {
        itemData.width = item.width;
        itemData.height = item.height;
        itemData.duration = item.duration;
        itemData.frameRate = item.frameRate;
        itemData.layers = [];

        // Export all layers
        for (var i = 1; i <= item.numLayers; i++) {
            itemData.layers.push(exportLayer(item.layer(i)));
        }
    } else if (item.typeName === "Footage") {
        itemData.width = item.width;
        itemData.height = item.height;
        itemData.duration = item.duration;
        if (item.file) {
            itemData.file = item.file.fsName;
        }
    }

    return itemData;
}

function exportLayer(layer) {
    var layerData = {
        name: layer.name,
        index: layer.index,
        enabled: layer.enabled,
        inPoint: layer.inPoint,
        outPoint: layer.outPoint,
        startTime: layer.startTime,
        stretch: layer.stretch,
        blendingMode: layer.blendingMode.toString(),
        properties: {},
        effects: []
    };

    // Export transform properties
    if (layer.transform) {
        layerData.properties.Transform = exportPropertyGroup(layer.transform);
    }

    // Export material options (for 3D layers)
    if (layer.threeDLayer && layer.materialOption) {
        layerData.properties.MaterialOptions = exportPropertyGroup(layer.materialOption);
    }

    // Export ALL effects (including Drop Shadow, Glow, etc.)
    if (layer.property("Effects")) {
        var effects = layer.property("Effects");
        for (var i = 1; i <= effects.numProperties; i++) {
            var effect = effects.property(i);
            layerData.effects.push(exportEffect(effect));
        }
    }

    // Export layer-specific properties (ExtendScript compatible)
    try {
        if (layer.property("Source Text")) {
            layerData.layerType = "text";
            layerData.properties.SourceText = exportProperty(layer.property("Source Text"));
        } else if (layer.property("Contents")) {
            layerData.layerType = "shape";
            layerData.properties.Contents = exportPropertyGroup(layer.property("Contents"));
        } else if (layer.source) {
            layerData.layerType = "av";
            layerData.source = {
                name: layer.source.name,
                id: layer.source.id
            };
        } else {
            layerData.layerType = "null";
        }
    } catch (e) {
        layerData.layerType = "unknown";
    }

    return layerData;
}

function exportEffect(effect) {
    var effectData = {
        name: effect.name,
        matchName: effect.matchName,
        enabled: effect.enabled,
        properties: {}
    };

    // Export all effect properties
    for (var i = 1; i <= effect.numProperties; i++) {
        var prop = effect.property(i);
        effectData.properties[prop.name] = exportProperty(prop);
    }

    return effectData;
}

function exportPropertyGroup(propGroup) {
    var groupData = {};
    
    for (var i = 1; i <= propGroup.numProperties; i++) {
        var prop = propGroup.property(i);
        groupData[prop.name] = exportProperty(prop);
    }
    
    return groupData;
}

function exportProperty(prop) {
    var propData = {
        name: prop.name,
        matchName: prop.matchName
    };

    try {
        propData.propertyType = prop.propertyType.toString();
    } catch (e) {
        propData.propertyType = "unknown";
    }

    try {
        if (prop.numKeys > 0) {
            // Animated property - export keyframes
            propData.keyframes = [];
            for (var i = 1; i <= prop.numKeys; i++) {
                var keyframe = {
                    time: prop.keyTime(i),
                    value: prop.keyValue(i)
                };

                // Add interpolation types safely
                try {
                    keyframe.inInterpolationType = prop.keyInInterpolationType(i).toString();
                    keyframe.outInterpolationType = prop.keyOutInterpolationType(i).toString();
                } catch (eInterp) {
                    // Some properties don't have interpolation types
                }

                // Add easing data if available
                try {
                    keyframe.inTemporalEase = prop.keyInTemporalEase(i);
                    keyframe.outTemporalEase = prop.keyOutTemporalEase(i);
                } catch (eEase) {
                    // Some properties don't support temporal easing
                }

                propData.keyframes.push(keyframe);
            }
        } else {
            // Static property
            propData.value = prop.value;
        }
    } catch (e) {
        propData.error = e.toString();
    }

    return propData;
}

// Main execution
try {
    var result = exportFullAEProject();
    
    // Save to file
    var file = File.saveDialog("Save AE project as JSON", "*.json");
    if (file) {
        file.open("w");
        file.write(JSON.stringify(result, null, 2));
        file.close();
        alert("Export complete!\nSaved to: " + file.fsName);
    }
} catch (error) {
    alert("Export failed: " + error.toString());
}

/*───────────────────────────────────────────────────────────────────────────
  Export Full Lottie-Style JSON (ES-3 safe)
  • Generic effect introspection (no switch-cases)
  • Exports expressions, keyframes, static values
  • Outputs “v,fr,ip,op,w,h,nm,ddd,assets,layers” schema
───────────────────────────────────────────────────────────────────────────*/

// 0) POLYFILLS ─────────────────────────────────────────────────────────────
if (typeof JSON==="undefined") JSON={};
if (typeof JSON.stringify!=="function") {
  JSON.stringify = function(v){
    function esc(s){return '"'+s.replace(/["\\\b\f\n\r\t]/g,
      function(c){return {'"':'\\"','\\':'\\\\','\b':'\\b','\f':'\\f',
                         '\n':'\\n','\r':'\\r','\t':'\\t'}[c];})+'"';}
    function ser(x){
      if (x===null) return "null";
      var t = typeof x;
      if (t==="number"||t==="boolean") return String(x);
      if (t==="string") return esc(x);
      if (x instanceof Array) {
        var a=[]; for (var i=0; i<x.length; i++) a.push(ser(x[i])||"null");
        return "["+a.join(",")+"]";
      }
      if (t==="object") {
        var o=[]; for (var k in x) if (x.hasOwnProperty(k))
          o.push(esc(k)+":"+ser(x[k]));
        return "{"+o.join(",")+"}";
      }
      return "null";
    }
    return ser(v);
  };
}
if (typeof Date.prototype.toISO!=="function") {
  Date.prototype.toISO = function(){
    function z(n){return (n<10?"0":"")+n;}
    return this.getUTCFullYear()+"-"+z(this.getUTCMonth()+1)+"-"+z(this.getUTCDate())+
           "T"+z(this.getUTCHours())+":"+z(this.getUTCMinutes())+":"+
           z(this.getUTCSeconds())+"Z";
  };
}

// 1) UTILS ─────────────────────────────────────────────────────────────────
function _str(v){ try{return v.toString()}catch(e){return "";} }
function _arr(v){ return (v!=null && v.length!==undefined) ? [].slice.call(v,0) : v; }
function _mark(m){ return {t:m.time,cm:m.comment,d:m.duration}; }

// 2) TIMING ─────────────────────────────────────────────────────────────────
var comp = app.project.activeItem;
if (!comp || !(comp instanceof CompItem)) {
  alert("Select an active composition first.");
  throw new Error("No active comp");
}
var fr = comp.frameRate;
function toFrame(t){ return Math.round(t*fr); }

// 3) DUMP PROPERTY (value OR keyframes + expressions) ────────────────────────
function dumpProp(p) {
  var o = { nm:p.name, mn:p.matchName, tp:_str(p.propertyType) };
  if (p.expressionEnabled) o.x = p.expression;

  try {
    if (p.numKeys && p.numKeys>0) {
      o.a = 1; o.k = [];
      for (var k=1; k<=p.numKeys; k++) {
        o.k.push({
          t: toFrame(p.keyTime(k)),
          s: _arr(p.keyValue(k)),
          inT: _str(p.keyInInterpolationType(k)),
          outT: _str(p.keyOutInterpolationType(k)),
          inE: p.keyInTemporalEase(k),
          outE: p.keyOutTemporalEase(k)
        });
      }
    } else {
      o.a = 0;
      o.k = _arr(p.value);
    }
  } catch(err) {
    o.err = err.toString();
  }

  // recurse into groups (shapes, masks…)
  if (p.numProperties && p.propertyType!==PropertyType.PROPERTY) {
    o.ch = {};
    for (var i=1; i<=p.numProperties; i++){
      var c = p.property(i);
      o.ch[c.name] = dumpProp(c);
    }
  }
  return o;
}

// 4) DUMP EFFECTS GENERICALLY ────────────────────────────────────────────────
function dumpEffects(layer) {
  var list = [];
  var ePar = layer.property("ADBE Effect Parade");
  if (!ePar) return list;

  for (var i=1; i<=ePar.numProperties; i++){
    var fx = ePar.property(i);
    var fxObj = {
      nm: fx.name,
      mn: fx.matchName,
      en: fx.enabled?1:0,
      props: {}
    };
    for (var j=1; j<=fx.numProperties; j++){
      var pr = fx.property(j);
      fxObj.props[ pr.name ] = dumpProp(pr);
    }
    list.push(fxObj);
  }
  return list;
}

// 5) DUMP TRANSFORM → Lottie’s “ks” ──────────────────────────────────────────
function dumpTransform(layer) {
  var tr = layer.property("ADBE Transform Group"), ks = {};
  if (!tr) return ks;

  // mapping AE matchName → Lottie key
  var MAP = [
    {m:"ADBE Opacity", k:"o", ix:11},
    {m:"ADBE Rotate Z",k:"r", ix:10},
    {m:"ADBE Position",k:"p", ix:2},
    {m:"ADBE Anchor Point",k:"a",ix:1},
    {m:"ADBE Scale",   k:"s", ix:6}
  ];
  for (var i=0; i<MAP.length; i++){
    var info=MAP[i], p=tr.property(info.m);
    if (!p) continue;
    var out={a:p.numKeys>0?1:0,ix:info.ix};
    if (out.a){
      out.k=[];
      for (var k=1;k<=p.numKeys;k++){
        out.k.push({t:toFrame(p.keyTime(k)),s:_arr(p.keyValue(k))});
      }
    } else {
      out.k=_arr(p.value);
    }
    ks[ info.k ] = out;
  }
  return ks;
}

// 6) DUMP ONE LAYER ─────────────────────────────────────────────────────────
function dumpLayer(layer){
  var L = {
    ddd: layer.threeDLayer?1:0,
    ind: layer.index,
    ty: ( layer.source instanceof CompItem?0
       : layer.property("Source Text")?5
       : layer.source && layer.source.file?2
       : 1),
    nm: layer.name,
    refId: "",
    sr: 1/layer.stretch,
    ks: dumpTransform(layer),
    ao: layer.autoOrient?1:0,
    ef: dumpEffects(layer),
    ip: toFrame(layer.inPoint),
    op: toFrame(layer.outPoint),
    st: toFrame(layer.startTime),
    bm: layer.blendingMode
  };
  if (layer.source){
    var src = layer.source;
    if (src instanceof CompItem)       L.refId = "comp_"+src.id;
    else if (src.file)                 L.refId = "img_"+ src.id;
  }
  return L;
}

// 7) COLLECT ASSETS ─────────────────────────────────────────────────────────
var assets = [];
for (var i=1;i<=app.project.numItems;i++){
  var it = app.project.item(i);
  if (it instanceof FootageItem){
    var T = it.mainSource;
    var type = T.isStill? "image" : (T.isAudio? "audio" : "footage");
    assets.push({
      id: type+"_"+it.id,
      w:  it.width||null,
      h:  it.height||null,
      u:  type+"s/",
      p:  it.file ? it.file.name : "",
      t:  (type==="audio"?2:1)
    });
  }
  else if (it instanceof CompItem){
    assets.push({ id:"comp_"+it.id, nm:it.name, fr:it.frameRate });
  }
}

// 8) BUILD & WRITE JSON ────────────────────────────────────────────────────
var lottie = {
  v:   "1.0.0",
  fr:  comp.frameRate,
  ip:  toFrame(comp.workAreaStart),
  op:  toFrame(comp.workAreaStart + comp.workAreaDuration),
  w:   comp.width,
  h:   comp.height,
  nm:  comp.name,
  ddd: 0,
  assets: assets,
  layers: (function(){
    var a=[]; for(var i=1;i<=comp.numLayers;i++) a.push(dumpLayer(comp.layer(i)));
    return a;
  })()
};

app.beginUndoGroup("Export Lottie-Style JSON");
try {
  var f = File.saveDialog("Save JSON", "*.json");
  if (f) {
    f.open("w"); f.encoding="UTF-8";
    f.write(JSON.stringify(lottie, null, 2));
    f.close();
    alert("Export complete:\n"+f.fsName);
  }
} catch(e){
  alert("Export failed:\n"+e.toString());
}
app.endUndoGroup();

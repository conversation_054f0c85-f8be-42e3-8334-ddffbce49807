#!/usr/bin/env python3
import json

# Load and analyze the cat.json file
with open('cat.json', 'r') as f:
    data = json.load(f)

print('=== LAYER ANALYSIS ===')
print(f'Total layers: {len(data["layers"])}')
print()

# Analyze each layer
layers_info = []
for i, layer in enumerate(data['layers']):
    ind = layer.get('ind', 'N/A')
    name = layer.get('nm', 'Unnamed')
    parent = layer.get('parent', None)
    layer_type = layer.get('ty', 'Unknown')
    
    parent_str = f' (parent: {parent})' if parent else ''
    
    info = {
        'order': i + 1,
        'index': ind,
        'name': name,
        'parent': parent,
        'type': layer_type,
        'has_parent': parent is not None
    }
    layers_info.append(info)
    
    print(f'{i+1:2d}. Index {ind:2d}: {name}{parent_str}')

print('\n=== PARENT-CHILD RELATIONSHIPS ===')
for info in layers_info:
    if info['has_parent']:
        parent_layer = next((l for l in layers_info if l['index'] == info['parent']), None)
        parent_name = parent_layer['name'] if parent_layer else 'Unknown'
        print(f'{info["name"]} -> {parent_name}')

print('\n=== LAYER ORDERING ANALYSIS ===')
print('Current JSON order (top to bottom in After Effects):')
for info in layers_info:
    print(f'{info["order"]:2d}. {info["name"]} (index {info["index"]})')

print('\n=== TAIL POSITION ANALYSIS ===')
tail_layer = next((l for l in layers_info if 'tail' in l['name'].lower()), None)
body_layer = next((l for l in layers_info if 'body' in l['name'].lower()), None)

if tail_layer and body_layer:
    print(f'Tail: Order {tail_layer["order"]}, Index {tail_layer["index"]}')
    print(f'Body: Order {body_layer["order"]}, Index {body_layer["index"]}')
    
    if tail_layer['order'] < body_layer['order']:
        print('✅ CORRECT: Tail appears BEFORE Body in JSON (will be behind in AE)')
    else:
        print('❌ WRONG: Tail appears AFTER Body in JSON (will be in front in AE)')
        print(f'   Tail should be moved from position {tail_layer["order"]} to before position {body_layer["order"]}')

#!/usr/bin/env python3
"""
INTELLIGENT ASSET DEPENDENCY MANAGER
Handles external file dependencies in Lottie JSON files with interactive file selection
"""

import json
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from typing import Dict, List, Tuple, Optional
import base64
from pathlib import Path

class AssetDependencyManager:
    def __init__(self):
        self.missing_assets = []
        self.resolved_assets = {}
        self.asset_mappings = {}
        
    def analyze_dependencies(self, json_file_path: str) -> Dict:
        """Analyze JSON file for external dependencies"""
        print("🔍 ANALYZING ASSET DEPENDENCIES...")
        
        with open(json_file_path, 'r', encoding='utf-8') as f:
            lottie_data = json.load(f)
        
        dependencies = {
            'images': [],
            'videos': [],
            'audio': [],
            'fonts': [],
            'precomps': [],
            'missing_count': 0
        }
        
        # Analyze assets
        if 'assets' in lottie_data:
            for asset in lottie_data['assets']:
                asset_info = self._analyze_asset(asset)
                if asset_info:
                    asset_type = asset_info['type']
                    if asset_type in dependencies:
                        dependencies[asset_type].append(asset_info)
                        if asset_info['missing']:
                            dependencies['missing_count'] += 1
        
        # Analyze fonts
        if 'fonts' in lottie_data:
            for font_list in lottie_data['fonts']['list']:
                font_info = {
                    'id': font_list.get('fName', 'Unknown'),
                    'name': font_list.get('fFamily', 'Unknown Font'),
                    'style': font_list.get('fStyle', 'Regular'),
                    'path': font_list.get('fPath', ''),
                    'missing': not font_list.get('fPath', '').strip(),
                    'type': 'font'
                }
                dependencies['fonts'].append(font_info)
                if font_info['missing']:
                    dependencies['missing_count'] += 1
        
        return dependencies
    
    def _analyze_asset(self, asset: Dict) -> Optional[Dict]:
        """Analyze individual asset for dependencies"""
        asset_id = asset.get('id', '')
        asset_name = asset.get('nm', asset_id)
        
        # Determine asset type
        asset_type = 'unknown'
        file_path = ''
        embedded = False
        
        if 'e' in asset:
            if asset['e'] == 0:
                asset_type = 'images'
                file_path = asset.get('u', '') + asset.get('p', '')
            elif asset['e'] == 1:
                asset_type = 'videos'
                file_path = asset.get('u', '') + asset.get('p', '')
        elif 't' in asset and asset['t'] == 2:
            asset_type = 'audio'
            file_path = asset.get('u', '') + asset.get('p', '')
        elif 'layers' in asset:
            asset_type = 'precomps'
            return None  # Precomps don't need external files
        
        # Check if asset is embedded (base64)
        if 'p' in asset and asset['p'].startswith('data:'):
            embedded = True
            file_path = 'embedded'
        
        if asset_type == 'unknown':
            return None
            
        return {
            'id': asset_id,
            'name': asset_name,
            'type': asset_type,
            'original_path': file_path,
            'embedded': embedded,
            'missing': not embedded and not file_path.strip(),
            'resolved_path': None
        }
    
    def interactive_asset_resolution(self, dependencies: Dict) -> Dict:
        """Interactive GUI for resolving missing assets"""
        if dependencies['missing_count'] == 0:
            print("✅ No missing assets found!")
            return dependencies
            
        print(f"⚠️  Found {dependencies['missing_count']} missing assets")
        print("🎯 Opening interactive asset resolver...")
        
        # Create GUI
        root = tk.Tk()
        root.title("🎬 Asset Dependency Resolver")
        root.geometry("800x600")
        root.configure(bg='#2b2b2b')
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), background='#2b2b2b', foreground='#4CAF50')
        style.configure('Info.TLabel', font=('Arial', 10), background='#2b2b2b', foreground='#E0E0E0')
        
        # Main frame
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="🎬 Resolve Missing Assets", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # Scrollable frame for assets
        canvas = tk.Canvas(main_frame, bg='#2b2b2b', highlightthickness=0)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Add missing assets to GUI
        self._create_asset_widgets(scrollable_frame, dependencies)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        def on_resolve():
            self._collect_resolved_paths()
            root.quit()
            
        def on_skip():
            root.quit()
        
        ttk.Button(button_frame, text="✅ Resolve All", command=on_resolve).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="⏭️ Skip Missing Assets", command=on_skip).pack(side=tk.LEFT)
        
        # Run GUI
        root.mainloop()
        root.destroy()
        
        return dependencies
    
    def _create_asset_widgets(self, parent, dependencies: Dict):
        """Create widgets for each missing asset"""
        row = 0
        
        for asset_type, assets in dependencies.items():
            if asset_type == 'missing_count' or not assets:
                continue
                
            # Type header
            type_label = ttk.Label(parent, text=f"📁 {asset_type.upper()}", style='Header.TLabel')
            type_label.grid(row=row, column=0, columnspan=3, sticky='w', pady=(10, 5))
            row += 1
            
            for asset in assets:
                if not asset.get('missing', False):
                    continue
                    
                # Asset info
                info_text = f"ID: {asset['id']} | Name: {asset['name']}"
                if asset.get('original_path'):
                    info_text += f" | Original: {asset['original_path']}"
                    
                info_label = ttk.Label(parent, text=info_text, style='Info.TLabel')
                info_label.grid(row=row, column=0, sticky='w', padx=(20, 10))
                
                # File selection
                path_var = tk.StringVar()
                path_entry = ttk.Entry(parent, textvariable=path_var, width=40)
                path_entry.grid(row=row, column=1, padx=(0, 10), sticky='ew')
                
                def browse_file(asset_id=asset['id'], var=path_var, asset_type=asset_type):
                    file_types = self._get_file_types(asset_type)
                    filename = filedialog.askopenfilename(
                        title=f"Select {asset_type[:-1]} file for {asset_id}",
                        filetypes=file_types
                    )
                    if filename:
                        var.set(filename)
                        self.resolved_assets[asset_id] = filename
                
                browse_btn = ttk.Button(parent, text="📂 Browse", command=browse_file)
                browse_btn.grid(row=row, column=2, padx=(0, 10))
                
                # Configure column weights
                parent.columnconfigure(1, weight=1)
                
                row += 1
    
    def _get_file_types(self, asset_type: str) -> List[Tuple[str, str]]:
        """Get file type filters for file dialog"""
        type_filters = {
            'images': [
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.webp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ],
            'videos': [
                ("Video files", "*.mp4 *.avi *.mov *.wmv *.flv *.webm *.mkv"),
                ("MP4 files", "*.mp4"),
                ("MOV files", "*.mov"),
                ("All files", "*.*")
            ],
            'audio': [
                ("Audio files", "*.mp3 *.wav *.aac *.ogg *.flac *.m4a"),
                ("MP3 files", "*.mp3"),
                ("WAV files", "*.wav"),
                ("All files", "*.*")
            ],
            'fonts': [
                ("Font files", "*.ttf *.otf *.woff *.woff2"),
                ("TrueType fonts", "*.ttf"),
                ("OpenType fonts", "*.otf"),
                ("All files", "*.*")
            ]
        }
        return type_filters.get(asset_type, [("All files", "*.*")])
    
    def _collect_resolved_paths(self):
        """Collect resolved paths from GUI"""
        # This would be implemented to collect the resolved paths
        # For now, we'll use the resolved_assets dict populated by browse_file
        pass
    
    def embed_assets_in_jsx(self, dependencies: Dict, jsx_code: List[str]) -> List[str]:
        """Embed resolved assets directly in JSX code"""
        print("🔧 EMBEDDING RESOLVED ASSETS...")
        
        # Add asset embedding functions to JSX
        asset_functions = [
            "",
            "// === ASSET EMBEDDING FUNCTIONS ===",
            "function embedImageAsset(comp, assetId, base64Data, width, height) {",
            "    // Create a solid layer as placeholder for embedded image",
            "    var layer = comp.layers.addSolid([1, 1, 1], assetId + '_embedded', width, height, 1);",
            "    layer.name = assetId + ' (Embedded Image)';",
            "    // Note: Actual image embedding would require additional AE scripting",
            "    return layer;",
            "}",
            "",
            "function createAssetReference(comp, assetId, filePath) {",
            "    // Create reference to external file",
            "    try {",
            "        var file = new File(filePath);",
            "        if (file.exists) {",
            "            // Import file and add to comp",
            "            var importOptions = new ImportOptions(file);",
            "            var footage = app.project.importFile(importOptions);",
            "            var layer = comp.layers.add(footage);",
            "            layer.name = assetId;",
            "            return layer;",
            "        } else {",
            "            alert('Asset file not found: ' + filePath);",
            "            return createNullLayer(comp, assetId + ' (Missing)');",
            "        }",
            "    } catch (e) {",
            "        alert('Error importing asset: ' + e.toString());",
            "        return createNullLayer(comp, assetId + ' (Error)');",
            "    }",
            "}",
            ""
        ]
        
        # Insert asset functions after helper functions
        helper_end_index = -1
        for i, line in enumerate(jsx_code):
            if "// === MAIN SCRIPT ===" in line:
                helper_end_index = i
                break
        
        if helper_end_index > 0:
            jsx_code[helper_end_index:helper_end_index] = asset_functions
        
        return jsx_code
    
    def generate_asset_report(self, dependencies: Dict) -> str:
        """Generate a detailed asset report"""
        report = ["🎬 ASSET DEPENDENCY REPORT", "=" * 50, ""]
        
        total_assets = sum(len(assets) for key, assets in dependencies.items() if key != 'missing_count')
        report.append(f"📊 Total Assets: {total_assets}")
        report.append(f"⚠️  Missing Assets: {dependencies['missing_count']}")
        report.append(f"✅ Resolved Assets: {len(self.resolved_assets)}")
        report.append("")
        
        for asset_type, assets in dependencies.items():
            if asset_type == 'missing_count' or not assets:
                continue
                
            report.append(f"📁 {asset_type.upper()}:")
            for asset in assets:
                status = "✅ RESOLVED" if asset['id'] in self.resolved_assets else ("❌ MISSING" if asset.get('missing') else "✅ OK")
                report.append(f"   • {asset['name']} ({asset['id']}) - {status}")
                if asset.get('original_path'):
                    report.append(f"     Original: {asset['original_path']}")
                if asset['id'] in self.resolved_assets:
                    report.append(f"     Resolved: {self.resolved_assets[asset['id']]}")
            report.append("")
        
        return "\n".join(report)

def main():
    """Test the asset dependency manager"""
    manager = AssetDependencyManager()
    
    # Test with a JSON file
    json_file = input("Enter JSON file path: ").strip()
    if not os.path.exists(json_file):
        print("❌ File not found!")
        return
    
    # Analyze dependencies
    deps = manager.analyze_dependencies(json_file)
    
    # Show report
    print(manager.generate_asset_report(deps))
    
    # Interactive resolution if needed
    if deps['missing_count'] > 0:
        manager.interactive_asset_resolution(deps)
        print("\n" + manager.generate_asset_report(deps))

if __name__ == "__main__":
    main()

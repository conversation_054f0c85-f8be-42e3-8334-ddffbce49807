// 01 - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// Create main composition: 01
var comp = app.project.items.addComp('01', 1080, 1080, 1.0, 6.000, 25);
var frameRate = 25;

// Asset References and Precomps

// Creating 14 layers
// Smart layer ordering: back to front based on semantic analysis
// L Leg: depth priority 0
// L Thigh: depth priority 5
// R B Thigh: depth priority 10
// R B Leg: depth priority 15
// Body: depth priority 20
// Tail: depth priority 25
// R F Thigh: depth priority 30
// l F Thigh: depth priority 35
// L FLeg: depth priority 40
// R Leg: depth priority 45
// Face: depth priority 50
// L Eyses: depth priority 55
// R Eyes: depth priority 55
// Mouth: depth priority 60
// Layer 11: L Thigh
var layer_11 = createShapeLayer(comp, 'L Thigh');
// Shape Group: Group 1
var layer_11_group_0 = layer_11.property('Contents').addProperty('ADBE Vector Group');
layer_11_group_0.name = 'Group 1';
var layer_11_group_0_path_0 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[20.729, 51.025], [25, -31.282], [-25, -30.282], [-7.102, 52.968], [5, 67.718]];
pathShape.inTangents = [[0, 0], [-0.056, 18.4], [8.23, -38.436], [-2.226, -23.294], [-14, -1]];
pathShape.outTangents = [[2.223, -25.362], [-0.41, -37.172], [0.139, 10.83], [0, 0], [14, 1]];
pathShape.closed = true;
layer_11_group_0_path_0.property('Path').setValue(pathShape);
var layer_11_group_0_fill_1 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_11_group_0_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_11_group_0_fill_1.property('Opacity').setValue(100);
var layer_11_group_0_transform = layer_11_group_0.property('Transform');
layer_11_group_0_transform.property('Position').setValue([25.25, 68.968]);
layer_11_group_0_transform.property('Scale').setValue([100, 100]);
layer_11_group_0_transform.property('Rotation').setValue(0);
layer_11_group_0_transform.property('Opacity').setValue(100);
layer_11.inPoint = 0.000000;
layer_11.outPoint = 6.250000;
layer_11.startTime = 0.000000;
layer_11.property('Transform').property('Opacity').setValue(100);
layer_11.property('Transform').property('Position').setValue([462, 607.566, 0]);
layer_11.property('Transform').property('Scale').setValue([130, 130, 100]);
layer_11.property('Transform').property('Rotation').setValue(0);
layer_11.property('Transform').property('Anchor Point').setValue([25.25, 26.66, 0]);

// Layer 10: L Leg
var layer_10 = createShapeLayer(comp, 'L Leg');
// Shape Group: Group 1
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Group 1';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[20.569, -14.417], [17.779, 8.065], [7.059, 26.267], [-18.303, 24.926], [-7.15, -4.678], [-7.262, -12.474], [4.84, -27.724]];
pathShape.inTangents = [[0, 0], [0.887, -3.547], [4.589, -1.373], [0.704, 2.564], [-2.216, 18.415], [0.298, 3.111], [-8, 0]];
pathShape.outTangents = [[-0.932, 10.624], [-2.059, 8.236], [-4.869, 1.457], [-2.266, -8.275], [0.246, -2.036], [0, 0], [8, 0]];
pathShape.closed = true;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_fill_1 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_10_group_0_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_10_group_0_fill_1.property('Opacity').setValue(100);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([20.819, 27.974]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
layer_10.inPoint = 0.000000;
layer_10.outPoint = 6.250000;
layer_10.startTime = 0.000000;
layer_10.parent = layer_11;
layer_10.property('Transform').property('Opacity').setValue(100);
layer_10.property('Transform').property('Position').setValue([30.795, 113.641, 0]);
layer_10.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_10.property('Transform').property('Rotation').setValue(0);
layer_10.property('Transform').property('Anchor Point').setValue([26.204, 7.205, 0]);

// Layer 12: R B Thigh
var layer_12 = createShapeLayer(comp, 'R B Thigh');
// Shape Group: Group 1
var layer_12_group_0 = layer_12.property('Contents').addProperty('ADBE Vector Group');
layer_12_group_0.name = 'Group 1';
var layer_12_group_0_path_0 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[50.613, 47.546], [51.826, 42.801], [41.338, 9.584], [-24.744, 12.614], [13.249, 51.036], [30.156, 59.925]];
pathShape.inTangents = [[0, 0], [-0.241, 1.565], [6.877, 21.213], [-28.138, -75.539], [-3.608, -14.039], [-13, -3]];
pathShape.outTangents = [[0.562, -1.605], [1.056, -6.827], [-35.207, -72.279], [19.113, 24.126], [0, 0], [13, 3]];
pathShape.closed = true;
layer_12_group_0_path_0.property('Path').setValue(pathShape);
var layer_12_group_0_fill_1 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_12_group_0_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_12_group_0_fill_1.property('Opacity').setValue(100);
var layer_12_group_0_transform = layer_12_group_0.property('Transform');
layer_12_group_0_transform.property('Position').setValue([53.132, 63.175]);
layer_12_group_0_transform.property('Scale').setValue([100, 100]);
layer_12_group_0_transform.property('Rotation').setValue(0);
layer_12_group_0_transform.property('Opacity').setValue(100);
layer_12.inPoint = 0.000000;
layer_12.outPoint = 6.250000;
layer_12.startTime = 0.000000;
layer_12.property('Transform').property('Opacity').setValue(100);
layer_12.property('Transform').property('Position').setValue([618.397, 640.798, 0]);
layer_12.property('Transform').property('Scale').setValue([130, 130, 100]);
layer_12.property('Transform').property('Rotation').setValue(0);
layer_12.property('Transform').property('Anchor Point').setValue([41.594, 41.637, 0]);

// Layer 13: R B Leg
var layer_13 = createShapeLayer(comp, 'R B Leg');
// Shape Group: Group 1
var layer_13_group_0 = layer_13.property('Contents').addProperty('ADBE Vector Group');
layer_13_group_0.name = 'Group 1';
var layer_13_group_0_path_0 = layer_13_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[27.912, -12.318], [11.877, 14.407], [2.378, 26.817], [-26.068, 25.71], [-9.452, -8.827], [2.933, -26.904]];
pathShape.inTangents = [[-1.542, -10.379], [7.573, -11.172], [4.371, -1.748], [0.851, 2.515], [3.574, 13.908], [-10.521, -1.966]];
pathShape.outTangents = [[1.158, 7.788], [-4.661, 6.876], [-5.13, 2.053], [-3.002, -8.857], [0, 0], [10.522, 1.966]];
pathShape.closed = true;
layer_13_group_0_path_0.property('Path').setValue(pathShape);
var layer_13_group_0_fill_1 = layer_13_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_13_group_0_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_13_group_0_fill_1.property('Opacity').setValue(100);
var layer_13_group_0_transform = layer_13_group_0.property('Transform');
layer_13_group_0_transform.property('Position').setValue([29.32, 29.121]);
layer_13_group_0_transform.property('Scale').setValue([100, 100]);
layer_13_group_0_transform.property('Rotation').setValue(0);
layer_13_group_0_transform.property('Opacity').setValue(100);
layer_13.inPoint = 0.000000;
layer_13.outPoint = 6.250000;
layer_13.startTime = 0.000000;
layer_13.parent = layer_12;
layer_13.property('Transform').property('Opacity').setValue(100);
layer_13.property('Transform').property('Position').setValue([88.478, 105.336, 0]);
layer_13.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_13.property('Transform').property('Rotation').setValue(0);
layer_13.property('Transform').property('Anchor Point').setValue([41.965, 11.418, 0]);

// Layer 9: Body
var layer_9 = createShapeLayer(comp, 'Body');
// Shape Group: Group 1
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 1';
var layer_9_group_0_path_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[10.278, -26.317], [-2.202, -24.851], [6.675, 28.002]];
pathShape.inTangents = [[-0.411, 19.835], [4.185, -0.469], [-5.949, 0.369]];
pathShape.outTangents = [[-4.139, 0.494], [2.61, 21.188], [6.228, -0.383]];
pathShape.closed = true;
layer_9_group_0_path_0.property('Path').setValue(pathShape);
var layer_9_group_0_path_1 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-63.905, 6.826], [-46.542, -24.04], [-58.579, -27.131]];
pathShape.inTangents = [[-4.638, -1.731], [-7.022, 15.307], [3.236, 1.408]];
pathShape.outTangents = [[4.64, 1.729], [-4.815, -0.654], [-5.446, 17.377]];
pathShape.closed = true;
layer_9_group_0_path_1.property('Path').setValue(pathShape);
var layer_9_group_0_path_2 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-31.226, -23.066], [-28.13, 28.002], [-18.515, -23.445]];
pathShape.inTangents = [[4.264, 0.032], [-4.795, -0.577], [-1.842, 22.487]];
pathShape.outTangents = [[-1.043, 24.968], [4.575, 0.55], [-4.216, 0.261]];
pathShape.closed = true;
layer_9_group_0_path_2.property('Path').setValue(pathShape);
var layer_9_group_0_path_3 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[64.673, -26.627], [54.519, -28.132], [64.414, 6.55]];
pathShape.inTangents = [[1.614, 11.412], [3.398, 0.342], [-3.866, 0.369]];
pathShape.outTangents = [[-3.368, -0.716], [2.374, 16.505], [4.129, -0.393]];
pathShape.closed = true;
layer_9_group_0_path_3.property('Path').setValue(pathShape);
var layer_9_group_0_path_4 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[35.221, -28.552], [24.716, -27.883], [37.393, 23.495]];
pathShape.inTangents = [[1.729, 16.656], [3.523, -0.325], [-6.229, 0.853]];
pathShape.outTangents = [[-3.487, 0.161], [2.9, 19.14], [5.163, -0.708]];
pathShape.closed = true;
layer_9_group_0_path_4.property('Path').setValue(pathShape);
var layer_9_group_0_fill_6 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_0_fill_6.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_9_group_0_fill_6.property('Opacity').setValue(100);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([148.775, 46.077]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_9_group_1 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_1.name = 'Group 2';
var layer_9_group_1_path_0 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-23.945, 44.65], [-66.861, -3.974], [-62.23, -59.937], [-36.997, -61.48], [-21.573, 23.033], [69.452, 61.48]];
pathShape.inTangents = [[17.455, 7.02], [1.808, 14.323], [0.309, 22.136], [-11.513, 0.929], [-7.442, -16.192], [-19.281, -4.058]];
pathShape.outTangents = [[-27.709, -11.144], [-2.591, -20.529], [3.017, 0.19], [-3.314, 46.899], [15.538, 33.797], [-49.771, -1.18]];
pathShape.closed = true;
layer_9_group_1_path_0.property('Path').setValue(pathShape);
var layer_9_group_1_fill_1 = layer_9_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_1_fill_1.property('Color').setValue([0.968628, 0.917647, 0.854902]);
layer_9_group_1_fill_1.property('Opacity').setValue(100);
var layer_9_group_1_transform = layer_9_group_1.property('Transform');
layer_9_group_1_transform.property('Position').setValue([70.363, 75.382]);
layer_9_group_1_transform.property('Scale').setValue([100, 100]);
layer_9_group_1_transform.property('Rotation').setValue(0);
layer_9_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_9_group_2 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_2.name = 'Group 3';
var layer_9_group_2_path_0 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-53.988, -73.151], [-127.538, -68.797], [-128.335, -4.526], [-85.419, 46.05], [111.991, 37.516], [82.416, -53.831], [-26.879, -50.968]];
pathShape.inTangents = [[0.598, 13.062], [6.604, 0.429], [-3.252, -18.355], [-26.678, -13.799], [-24.355, 35.635], [40.124, 8.942], [38.279, 3.813]];
pathShape.outTangents = [[-8.256, 0], [0.309, 23.023], [2.614, 14.765], [43.394, 22.443], [19.596, -28.673], [-34.674, -7.729], [-19.401, -1.933]];
pathShape.closed = true;
layer_9_group_2_path_0.property('Path').setValue(pathShape);
var layer_9_group_2_fill_1 = layer_9_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_2_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_9_group_2_fill_1.property('Opacity').setValue(100);
var layer_9_group_2_transform = layer_9_group_2.property('Transform');
layer_9_group_2_transform.property('Position').setValue([131.837, 73.401]);
layer_9_group_2_transform.property('Scale').setValue([100, 100]);
layer_9_group_2_transform.property('Rotation').setValue(0);
layer_9_group_2_transform.property('Opacity').setValue(100);
layer_9.inPoint = 0.000000;
layer_9.outPoint = 6.250000;
layer_9.startTime = 0.000000;
layer_9.property('Transform').property('Opacity').setValue(100);
// Smart detection: 115 keyframes for layer_9.property('Transform').property('Position')
layer_9.property('Transform').property('Position').setValueAtTime(3/frameRate, [557.916, 629.378, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(4/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(5/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(6/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(7/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(8/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(9/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(10/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(11/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(12/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(13/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(14/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(15/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(16/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(17/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(18/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(19/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(20/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(21/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(23/frameRate, [557.916, 638.803, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(29/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(30/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(31/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(32/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(33/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(34/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(35/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(36/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(37/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(38/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(39/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(40/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(41/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(42/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(43/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(44/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(45/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(46/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(48/frameRate, [557.916, 629.161, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(54/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(55/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(56/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(57/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(58/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(59/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(60/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(61/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(62/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(63/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(64/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(65/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(66/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(67/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(68/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(69/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(70/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(71/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(73/frameRate, [557.916, 638.803, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(79/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(80/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(81/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(82/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(83/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(84/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(85/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(86/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(87/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(88/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(89/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(90/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(91/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(92/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(93/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(94/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(95/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(96/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(98/frameRate, [557.916, 629.161, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(104/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(105/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(106/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(107/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(108/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(109/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(110/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(111/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(112/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(113/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(114/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(115/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(116/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(117/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(118/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(119/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(120/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(121/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(123/frameRate, [557.916, 638.803, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(129/frameRate, [557.916, 638.295, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(130/frameRate, [557.916, 637.938, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(131/frameRate, [557.916, 637.524, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(132/frameRate, [557.916, 637.064, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(133/frameRate, [557.916, 636.565, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(134/frameRate, [557.916, 636.026, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(135/frameRate, [557.916, 635.459, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(136/frameRate, [557.916, 634.874, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(137/frameRate, [557.916, 634.278, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(138/frameRate, [557.916, 633.68, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(139/frameRate, [557.916, 633.086, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(140/frameRate, [557.916, 632.502, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(141/frameRate, [557.916, 631.937, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(142/frameRate, [557.916, 631.398, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(143/frameRate, [557.916, 630.896, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(144/frameRate, [557.916, 630.434, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(145/frameRate, [557.916, 630.022, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(146/frameRate, [557.916, 629.667, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(148/frameRate, [557.916, 629.161, 0]);
layer_9.property('Transform').property('Scale').setValue([130, 130, 100]);
layer_9.property('Transform').property('Rotation').setValue(0);
layer_9.property('Transform').property('Anchor Point').setValue([131.837, 73.401, 0]);

// Layer 14: Tail
var layer_14 = createShapeLayer(comp, 'Tail');
// Shape Group: Group 1
var layer_14_group_0 = layer_14.property('Contents').addProperty('ADBE Vector Group');
layer_14_group_0.name = 'Group 1';
var layer_14_group_0_path_0 = layer_14_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[25.453, 7.137], [18.886, 27.904], [42.068, 36.509], [49.612, 11.978]];
pathShape.inTangents = [[0, 0], [4.305, -6.555], [0, 0], [-0.746, 8.417]];
pathShape.outTangents = [[-0.101, 7.155], [0, 0], [4.292, -7.844], [0, 0]];
pathShape.closed = true;
layer_14_group_0_path_0.property('Path').setValue(pathShape);
var layer_14_group_0_path_1 = layer_14_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-0.262, 44.943], [-19.812, 52.611], [-9.973, 75.413], [13.366, 65.396]];
pathShape.inTangents = [[0, 0], [7.452, -1.906], [0, 0], [-6.801, 4.061]];
pathShape.outTangents = [[-5.574, 3.161], [0, 0], [8.751, -2.555], [0, 0]];
pathShape.closed = true;
layer_14_group_0_path_1.property('Path').setValue(pathShape);
var layer_14_group_0_path_2 = layer_14_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[17.602, -17.134], [35.517, -33.754], [15.897, -50.315], [0.366, -31.009]];
pathShape.inTangents = [[-4.226, -6.025], [0, 0], [7.305, 4.174], [0, 0]];
pathShape.outTangents = [[0, 0], [-5.576, -7.061], [0, 0], [7.003, 3.519]];
pathShape.closed = true;
layer_14_group_0_path_2.property('Path').setValue(pathShape);
var layer_14_group_0_path_3 = layer_14_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-49.612, -52.067], [-27.539, -41.872], [-17.188, -64.067], [-40.656, -75.413]];
pathShape.inTangents = [[0, 0], [-7.476, -2.7], [0, 0], [4.976, 4.131]];
pathShape.outTangents = [[6.939, 4.289], [0, 0], [-10.819, -3.879], [0, 0]];
pathShape.closed = true;
layer_14_group_0_path_3.property('Path').setValue(pathShape);
var layer_14_group_0_fill_5 = layer_14_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_14_group_0_fill_5.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_14_group_0_fill_5.property('Opacity').setValue(100);
var layer_14_group_0_transform = layer_14_group_0.property('Transform');
layer_14_group_0_transform.property('Position').setValue([93.284, 100.948]);
layer_14_group_0_transform.property('Scale').setValue([100, 100]);
layer_14_group_0_transform.property('Rotation').setValue(0);
layer_14_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_14_group_1 = layer_14.property('Contents').addProperty('ADBE Vector Group');
layer_14_group_1.name = 'Group 2';
var layer_14_group_1_path_0 = layer_14_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-25.61, 90.534], [58.255, 52.429], [57.687, -20.22], [6.161, -52.529], [-28.503, -78.961], [-41.868, -89.839], [-52.746, -76.471], [-1.878, -29.522], [37.51, -6.56], [38.095, 38.715], [-57.525, 62.435], [-72.064, 71.682], [-62.799, 86.223]];
pathShape.inTangents = [[-11.389, 0], [-13.64, 20.051], [15.602, 23.072], [18.201, 6.361], [1.374, 13.449], [6.463, -0.696], [-0.695, -6.702], [-21.277, -7.434], [-8.149, -12.047], [9.576, -14.063], [50.96, 11.289], [1.465, -6.564], [-6.573, -1.458]];
pathShape.outTangents = [[41.988, 0], [15.254, -22.422], [-12.671, -18.741], [-23.035, -8.048], [-0.696, -6.692], [-6.704, 0.686], [3.095, 30.269], [16.059, 5.612], [9.942, 14.704], [-12.268, 18.017], [-6.446, -1.493], [-1.445, 6.575], [13.385, 2.985]];
pathShape.closed = true;
layer_14_group_1_path_0.property('Path').setValue(pathShape);
var layer_14_group_1_fill_1 = layer_14_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_14_group_1_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_14_group_1_fill_1.property('Opacity').setValue(100);
var layer_14_group_1_transform = layer_14_group_1.property('Transform');
layer_14_group_1_transform.property('Position').setValue([73.759, 90.785]);
layer_14_group_1_transform.property('Scale').setValue([100, 100]);
layer_14_group_1_transform.property('Rotation').setValue(0);
layer_14_group_1_transform.property('Opacity').setValue(100);
layer_14.inPoint = 0.000000;
layer_14.outPoint = 6.250000;
layer_14.startTime = 0.000000;
layer_14.parent = layer_9;
layer_14.property('Transform').property('Opacity').setValue(100);
layer_14.property('Transform').property('Position').setValue([206.303, 32.728, 0]);
layer_14.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 77 keyframes for layer_14.property('Transform').property('Rotation')
layer_14.property('Transform').property('Rotation').setValueAtTime(25/frameRate, 0);
layer_14.property('Transform').property('Rotation').setValueAtTime(26/frameRate, 1.171);
layer_14.property('Transform').property('Rotation').setValueAtTime(27/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(28/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(29/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(30/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(31/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(32/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(33/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(34/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(35/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(36/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(37/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(39/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(40/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(41/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(42/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(43/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(46/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(47/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(48/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 1.171);
layer_14.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 0);
layer_14.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 0);
layer_14.property('Transform').property('Rotation').setValueAtTime(76/frameRate, 1.171);
layer_14.property('Transform').property('Rotation').setValueAtTime(77/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(78/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(79/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(80/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(81/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(82/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(83/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(84/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(85/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(86/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(87/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(88/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(89/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(90/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(91/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(92/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(93/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(94/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(95/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(96/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(97/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(98/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(99/frameRate, 1.171);
layer_14.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 0);
layer_14.property('Transform').property('Rotation').setValueAtTime(125/frameRate, 0);
layer_14.property('Transform').property('Rotation').setValueAtTime(126/frameRate, 1.171);
layer_14.property('Transform').property('Rotation').setValueAtTime(127/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(128/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(129/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(130/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(131/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(132/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(133/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(134/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(135/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(136/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(137/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(138/frameRate, 64.14);
layer_14.property('Transform').property('Rotation').setValueAtTime(139/frameRate, 61.88);
layer_14.property('Transform').property('Rotation').setValueAtTime(140/frameRate, 57.74);
layer_14.property('Transform').property('Rotation').setValueAtTime(141/frameRate, 52.114);
layer_14.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 45.4);
layer_14.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 37.993);
layer_14.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 30.289);
layer_14.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 22.683);
layer_14.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 15.573);
layer_14.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 9.354);
layer_14.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 4.421);
layer_14.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 1.171);
layer_14.property('Transform').property('Anchor Point').setValue([4.212, 165.389, 0]);

// Layer 1: R F Thigh
var layer_1 = createShapeLayer(comp, 'R F Thigh');
// Shape Group: Group 1
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Group 1';
var layer_1_group_0_path_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[59.188, 37.725], [23.704, -1.349], [-27.5, -48.5], [-63.5, 1.5], [-35.3, 24.409], [24.345, 50.166], [48.5, 57.5]];
pathShape.inTangents = [[0, 0], [5.003, 16.089], [39, -13], [0, -24.334], [-13.2, 2.091], [-6.328, -5.355], [-14.999, 4]];
pathShape.outTangents = [[-6.758, -8.313], [-2.937, -9.437], [-27.511, 9.171], [0, 9], [43.26, -6.857], [0, 0], [15, -4]];
pathShape.closed = true;
layer_1_group_0_path_0.property('Path').setValue(pathShape);
var layer_1_group_0_fill_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_1_group_0_fill_1.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([63.75, 61.75]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.250000;
layer_1.startTime = 0.000000;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([649.666, 645.648, 0]);
layer_1.property('Transform').property('Scale').setValue([130, 130, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([29.609, 41.518, 0]);

// Layer 2: l F Thigh
var layer_2 = createShapeLayer(comp, 'l F Thigh');
// Shape Group: Group 1
var layer_2_group_0 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_0.name = 'Group 1';
var layer_2_group_0_path_0 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-7.368, 59.313], [20.357, 19.051], [-8.386, -51.844], [-40.318, 54.775], [-31.184, 70.151]];
pathShape.inTangents = [[0, 0], [-9.835, 10.426], [30.207, -23.308], [20.154, -39.766], [-15, -5]];
pathShape.outTangents = [[8.661, -14.58], [25.827, -27.38], [-20.173, 15.566], [0, 0], [15, 5]];
pathShape.closed = true;
layer_2_group_0_path_0.property('Path').setValue(pathShape);
var layer_2_group_0_fill_1 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_0_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_2_group_0_fill_1.property('Opacity').setValue(100);
var layer_2_group_0_transform = layer_2_group_0.property('Transform');
layer_2_group_0_transform.property('Position').setValue([46.434, 75.402]);
layer_2_group_0_transform.property('Scale').setValue([100, 100]);
layer_2_group_0_transform.property('Rotation').setValue(0);
layer_2_group_0_transform.property('Opacity').setValue(100);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 6.250000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
layer_2.property('Transform').property('Position').setValue([473.539, 625.403, 0]);
layer_2.property('Transform').property('Scale').setValue([130, 130, 100]);
layer_2.property('Transform').property('Rotation').setValue(0);
layer_2.property('Transform').property('Anchor Point').setValue([54.126, 49.248, 0]);

// Layer 3: L FLeg
var layer_3 = createShapeLayer(comp, 'L FLeg');
// Shape Group: Group 1
var layer_3_group_0 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_0.name = 'Group 1';
var layer_3_group_0_path_0 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[28.719, -11.131], [-5.524, 31.266], [-32.442, 32.031], [-36.792, 27.33], [-24.145, 10.046], [-4.231, -15.669], [11.903, -30.292]];
pathShape.inTangents = [[0, 0], [10.298, -1.495], [6.759, 0.194], [-0.111, 2.336], [-6.244, 5.353], [-4.779, 9.429], [-9.658, -1.932]];
pathShape.outTangents = [[-12.571, 21.161], [-4.969, 0.719], [-2.048, -0.058], [0.232, -4.934], [8.751, -7.504], [0, 0], [25, 5]];
pathShape.closed = true;
layer_3_group_0_path_0.property('Path').setValue(pathShape);
var layer_3_group_0_fill_1 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_0_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_3_group_0_fill_1.property('Opacity').setValue(100);
var layer_3_group_0_transform = layer_3_group_0.property('Transform');
layer_3_group_0_transform.property('Position').setValue([37.153, 32.475]);
layer_3_group_0_transform.property('Scale').setValue([100, 100]);
layer_3_group_0_transform.property('Rotation').setValue(0);
layer_3_group_0_transform.property('Opacity').setValue(100);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 6.250000;
layer_3.startTime = 0.000000;
layer_3.parent = layer_2;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([24.962, 124.307, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_3.property('Transform').property('Rotation').setValue(0);
layer_3.property('Transform').property('Anchor Point').setValue([51.768, 10.936, 0]);

// Layer 8: R Leg
var layer_8 = createShapeLayer(comp, 'R Leg');
// Shape Group: Group 1
var layer_8_group_0 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_0.name = 'Group 1';
var layer_8_group_0_path_0 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[14.492, -18.986], [15.379, 35.704], [-12.277, 35.062], [-8.116, 17.335], [-20.351, -6.545], [-11.195, -26.21]];
pathShape.inTangents = [[0, 0], [10.817, -13.963], [1.515, 3.148], [-1.174, 9.07], [4.547, 3.846], [-15.001, 12]];
pathShape.outTangents = [[1.271, 1.563], [-1.427, 1.843], [-1.774, -3.681], [1.163, -8.977], [0, 0], [14.999, -12]];
pathShape.closed = true;
layer_8_group_0_path_0.property('Path').setValue(pathShape);
var layer_8_group_0_fill_1 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_0_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_8_group_0_fill_1.property('Opacity').setValue(100);
var layer_8_group_0_transform = layer_8_group_0.property('Transform');
layer_8_group_0_transform.property('Position').setValue([26.446, 38.461]);
layer_8_group_0_transform.property('Scale').setValue([100, 100]);
layer_8_group_0_transform.property('Rotation').setValue(0);
layer_8_group_0_transform.property('Opacity').setValue(100);
layer_8.inPoint = 0.000000;
layer_8.outPoint = 6.250000;
layer_8.startTime = 0.000000;
layer_8.parent = layer_1;
layer_8.property('Transform').property('Opacity').setValue(100);
layer_8.property('Transform').property('Position').setValue([99.595, 95.7, 0]);
layer_8.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_8.property('Transform').property('Rotation').setValue(0);
layer_8.property('Transform').property('Anchor Point').setValue([17.595, 15.7, 0]);

// Layer 7: Face
var layer_7 = createShapeLayer(comp, 'Face');
// Shape Group: Group 1
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 1';
var layer_7_group_0_path_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-7.519, -5.39], [8.136, -2.543], [8.597, 0.186], [-0.416, 5.286], [-2.27, 4.949], [-8.912, -2.997]];
pathShape.inTangents = [[-1.354, -0.246], [0, 0], [1.198, -0.678], [0, 0], [0.456, 0.545], [0, 0]];
pathShape.outTangents = [[0, 0], [1.354, 0.246], [0, 0], [-0.619, 0.35], [0, 0], [-0.883, -1.057]];
pathShape.closed = true;
layer_7_group_0_path_0.property('Path').setValue(pathShape);
var layer_7_group_0_fill_1 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_0_fill_1.property('Color').setValue([0.003922, 0.000000, 0.007843]);
layer_7_group_0_fill_1.property('Opacity').setValue(100);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([140.532, 145.975]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_7_group_1 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_1.name = 'Group 2';
var layer_7_group_1_path_0 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[85.27, 20.48], [15.732, -18.896], [1.396, -41.052], [-20.107, -27.369], [-85.27, 0], [-1.285, 42.62]];
pathShape.inTangents = [[0, 0], [0, 0], [11.729, 3.91], [0, 0], [0, 0], [-54.159, -2.342]];
pathShape.outTangents = [[-51.665, 13.964], [0, 0], [-11.729, -3.909], [-18.246, 33.233], [0, 0], [54.159, 2.341]];
pathShape.closed = true;
layer_7_group_1_path_0.property('Path').setValue(pathShape);
var layer_7_group_1_fill_1 = layer_7_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_1_fill_1.property('Color').setValue([0.968628, 0.917647, 0.854902]);
layer_7_group_1_fill_1.property('Opacity').setValue(100);
var layer_7_group_1_transform = layer_7_group_1.property('Transform');
layer_7_group_1_transform.property('Position').setValue([140.556, 167.194]);
layer_7_group_1_transform.property('Scale').setValue([100, 100]);
layer_7_group_1_transform.property('Rotation').setValue(0);
layer_7_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_7_group_2 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_2.name = 'Group 3';
var layer_7_group_2_path_0 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[21.312, -5.046], [9.852, -5.627], [-1.54, -3.858], [-7.057, -2.134], [-12.326, 0.251], [-21.964, 6.658], [-12.443, 0.031], [-7.193, -2.483], [-1.671, -4.34], [9.803, -6.376], [21.501, -6.028], [21.924, -5.461], [21.357, -5.039], [21.349, -5.04]];
pathShape.inTangents = [[0, 0], [3.831, -0.2], [3.725, -0.974], [1.831, -0.605], [1.68, -0.958], [3.027, -2.404], [-3.462, 1.771], [-1.763, 0.816], [-1.889, 0.469], [-3.886, 0.274], [-3.91, -0.576], [0.04, -0.272], [0.273, 0.041], [0.002, 0.001]];
pathShape.outTangents = [[-3.737, -0.632], [-3.843, 0.187], [-1.883, 0.425], [-1.765, 0.773], [-3.484, 1.689], [2.973, -2.467], [1.669, -1.001], [1.828, -0.649], [3.737, -1.064], [3.891, -0.282], [0.273, 0.041], [-0.04, 0.274], [-0.003, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_2_path_0.property('Path').setValue(pathShape);
var layer_7_group_2_fill_1 = layer_7_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_2_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_2_fill_1.property('Opacity').setValue(100);
var layer_7_group_2_transform = layer_7_group_2.property('Transform');
layer_7_group_2_transform.property('Position').setValue([34.794, 153.823]);
layer_7_group_2_transform.property('Scale').setValue([100, 100]);
layer_7_group_2_transform.property('Rotation').setValue(0);
layer_7_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_7_group_3 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_3.name = 'Group 4';
var layer_7_group_3_path_0 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[27.602, 2.874], [13.746, -0.658], [-0.477, -2.342], [-14.77, -1.574], [-21.739, 0.111], [-28.336, 2.928], [-21.778, -0.009], [-14.814, -1.82], [-0.46, -2.842], [13.878, -1.397], [27.926, 1.928], [28.253, 2.556], [27.626, 2.883], [27.621, 2.881]];
pathShape.inTangents = [[0, 0], [4.687, 0.892], [4.775, 0.21], [4.721, -0.78], [2.28, -0.732], [2.116, -1.127], [-2.264, 0.799], [-2.358, 0.455], [-4.803, -0.123], [-4.74, -0.803], [-4.619, -1.449], [0.083, -0.264], [0.264, 0.083], [0.002, 0.001]];
pathShape.outTangents = [[-4.503, -1.499], [-4.695, -0.88], [-4.773, -0.206], [-2.355, 0.412], [-2.27, 0.757], [2.096, -1.164], [2.273, -0.773], [4.728, -0.866], [4.806, 0.128], [4.742, 0.819], [0.263, 0.083], [-0.082, 0.263], [-0.002, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_3_path_0.property('Path').setValue(pathShape);
var layer_7_group_3_fill_1 = layer_7_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_3_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_3_fill_1.property('Opacity').setValue(100);
var layer_7_group_3_transform = layer_7_group_3.property('Transform');
layer_7_group_3_transform.property('Position').setValue([28.586, 137.161]);
layer_7_group_3_transform.property('Scale').setValue([100, 100]);
layer_7_group_3_transform.property('Rotation').setValue(0);
layer_7_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_7_group_4 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_4.name = 'Group 5';
var layer_7_group_4_path_0 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[27.06, 6.223], [14.116, 0.146], [0.464, -4.183], [-13.72, -6.117], [-20.88, -5.774], [-27.891, -4.248], [-20.897, -5.898], [-13.716, -6.367], [0.574, -4.671], [14.384, -0.554], [27.555, 5.355], [27.759, 6.032], [27.081, 6.236], [27.077, 6.233]];
pathShape.inTangents = [[0, 0], [4.435, 1.759], [4.65, 1.104], [4.783, 0.122], [2.377, -0.289], [2.29, -0.709], [-2.373, 0.359], [-2.402, 0.003], [-4.695, -1.024], [-4.505, -1.68], [-4.264, -2.292], [0.131, -0.243], [0.244, 0.131], [0.002, 0.001]];
pathShape.outTangents = [[-4.141, -2.319], [-4.446, -1.747], [-4.649, -1.1], [-2.39, -0.038], [-2.372, 0.317], [2.278, -0.749], [2.378, -0.331], [4.806, 0.039], [4.695, 1.03], [4.503, 1.697], [0.243, 0.131], [-0.131, 0.243], [-0.001, -0.001], [0, 0]];
pathShape.closed = true;
layer_7_group_4_path_0.property('Path').setValue(pathShape);
var layer_7_group_4_fill_1 = layer_7_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_4_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_4_fill_1.property('Opacity').setValue(100);
var layer_7_group_4_transform = layer_7_group_4.property('Transform');
layer_7_group_4_transform.property('Position').setValue([30.238, 127.183]);
layer_7_group_4_transform.property('Scale').setValue([100, 100]);
layer_7_group_4_transform.property('Rotation').setValue(0);
layer_7_group_4_transform.property('Opacity').setValue(100);
// Shape Group: Group 6
var layer_7_group_5 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_5.name = 'Group 6';
var layer_7_group_5_path_0 = layer_7_group_5.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-17.784, -13.56], [-6.712, -9.768], [3.311, -3.823], [7.827, -0.141], [11.855, 4.06], [18.434, 13.615], [11.668, 4.226], [7.576, 0.138], [3.02, -3.417], [-7.02, -9.084], [-17.952, -12.574], [-17.986, -12.581], [-18.382, -13.167], [-17.794, -13.562]];
pathShape.inTangents = [[-0.002, 0], [-3.542, -1.634], [-3.123, -2.312], [-1.483, -1.251], [-1.21, -1.523], [-1.914, -3.356], [2.666, 2.807], [1.38, 1.344], [1.613, 1.06], [3.531, 1.528], [3.72, 0.724], [0, 0], [-0.052, 0.271], [-0.271, -0.053]];
pathShape.outTangents = [[3.863, 0.838], [3.541, 1.625], [1.602, 1.104], [1.363, 1.385], [2.617, 2.877], [-1.988, -3.317], [-1.235, -1.489], [-1.501, -1.211], [-3.144, -2.224], [-3.515, -1.535], [0, 0], [-0.271, -0.052], [0.053, -0.271], [0.002, 0.001]];
pathShape.closed = true;
layer_7_group_5_path_0.property('Path').setValue(pathShape);
var layer_7_group_5_fill_1 = layer_7_group_5.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_5_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_5_fill_1.property('Opacity').setValue(100);
var layer_7_group_5_transform = layer_7_group_5.property('Transform');
layer_7_group_5_transform.property('Position').setValue([231.1, 185.833]);
layer_7_group_5_transform.property('Scale').setValue([100, 100]);
layer_7_group_5_transform.property('Rotation').setValue(0);
layer_7_group_5_transform.property('Opacity').setValue(100);
// Shape Group: Group 7
var layer_7_group_6 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_6.name = 'Group 7';
var layer_7_group_6_path_0 = layer_7_group_6.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-25.885, -10.365], [-11.566, -8.533], [2.364, -4.838], [15.439, 1.171], [21.319, 5.319], [26.423, 10.376], [21.24, 5.416], [15.311, 1.386], [2.202, -4.365], [-11.702, -7.796], [-25.915, -9.365], [-25.933, -9.366], [-26.412, -9.885], [-25.894, -10.365]];
pathShape.inTangents = [[-0.002, 0], [-4.726, -0.902], [-4.543, -1.573], [-4.12, -2.474], [-1.856, -1.524], [-1.552, -1.828], [1.859, 1.508], [2.058, 1.215], [4.54, 1.488], [4.704, 0.83], [4.743, 0.182], [0, 0], [-0.011, 0.276], [-0.277, -0.011]];
pathShape.outTangents = [[4.833, 0.27], [4.72, 0.918], [4.539, 1.575], [2.047, 1.256], [1.837, 1.545], [-1.583, -1.801], [-1.875, -1.487], [-4.145, -2.392], [-4.543, -1.484], [-4.701, -0.814], [0, 0], [-0.276, -0.011], [0.01, -0.276], [0.002, 0]];
pathShape.closed = true;
layer_7_group_6_path_0.property('Path').setValue(pathShape);
var layer_7_group_6_fill_1 = layer_7_group_6.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_6_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_6_fill_1.property('Opacity').setValue(100);
var layer_7_group_6_transform = layer_7_group_6.property('Transform');
layer_7_group_6_transform.property('Position').setValue([242.064, 174.414]);
layer_7_group_6_transform.property('Scale').setValue([100, 100]);
layer_7_group_6_transform.property('Rotation').setValue(0);
layer_7_group_6_transform.property('Opacity').setValue(100);
// Shape Group: Group 8
var layer_7_group_7 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_7.name = 'Group 8';
var layer_7_group_7_path_0 = layer_7_group_7.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-27.404, -4.818], [-12.996, -5.712], [1.379, -4.704], [15.352, -1.261], [21.909, 1.706], [27.873, 5.712], [21.849, 1.816], [15.268, -1.026], [1.312, -4.208], [-12.99, -4.962], [-27.245, -3.831], [-27.262, -3.828], [-27.832, -4.247], [-27.413, -4.817]];
pathShape.inTangents = [[-0.002, 0], [-4.812, 0.003], [-4.758, -0.689], [-4.512, -1.656], [-2.11, -1.148], [-1.868, -1.503], [2.109, 1.132], [2.25, 0.806], [4.738, 0.607], [4.775, -0.07], [4.692, -0.713], [0, 0], [0.042, 0.273], [-0.273, 0.041]];
pathShape.outTangents = [[4.797, -0.644], [4.808, 0.013], [4.755, 0.694], [2.247, 0.848], [2.095, 1.171], [-1.894, -1.47], [-2.122, -1.107], [-4.521, -1.569], [-4.741, -0.604], [-4.771, 0.084], [0, 0], [-0.273, 0.041], [-0.041, -0.273], [0.002, 0]];
pathShape.closed = true;
layer_7_group_7_path_0.property('Path').setValue(pathShape);
var layer_7_group_7_fill_1 = layer_7_group_7.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_7_fill_1.property('Color').setValue([0.011765, 0.011765, 0.011765]);
layer_7_group_7_fill_1.property('Opacity').setValue(100);
var layer_7_group_7_transform = layer_7_group_7.property('Transform');
layer_7_group_7_transform.property('Position').setValue([244.69, 162.284]);
layer_7_group_7_transform.property('Scale').setValue([100, 100]);
layer_7_group_7_transform.property('Rotation').setValue(0);
layer_7_group_7_transform.property('Opacity').setValue(100);
// Shape Group: Group 9
var layer_7_group_8 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_8.name = 'Group 9';
var layer_7_group_8_path_0 = layer_7_group_8.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[7.375, -2.196], [-7.922, 1.613], [-7.922, 2.613], [8.082, -1.488]];
pathShape.inTangents = [[0.482, -0.423], [5.22, -0.047], [-0.645, 0.006], [-4.37, 3.835]];
pathShape.outTangents = [[-4.145, 3.637], [-0.643, 0.006], [5.52, -0.05], [0.485, -0.425]];
pathShape.closed = true;
layer_7_group_8_path_0.property('Path').setValue(pathShape);
var layer_7_group_8_fill_1 = layer_7_group_8.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_8_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_8_fill_1.property('Opacity').setValue(100);
var layer_7_group_8_transform = layer_7_group_8.property('Transform');
layer_7_group_8_transform.property('Position').setValue([143.91, 39.621]);
layer_7_group_8_transform.property('Scale').setValue([100, 100]);
layer_7_group_8_transform.property('Rotation').setValue(0);
layer_7_group_8_transform.property('Opacity').setValue(100);
// Shape Group: Group 10
var layer_7_group_9 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_9.name = 'Group 10';
var layer_7_group_9_path_0 = layer_7_group_9.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[5.577, -0.074], [-5.578, -0.926], [-5.578, 0.074], [5.577, 0.926]];
pathShape.inTangents = [[0.639, 0.049], [3.718, 0.284], [-0.638, -0.049], [-3.718, -0.284]];
pathShape.outTangents = [[-3.718, -0.284], [-0.641, -0.049], [3.718, 0.284], [0.642, 0.049]];
pathShape.closed = true;
layer_7_group_9_path_0.property('Path').setValue(pathShape);
var layer_7_group_9_fill_1 = layer_7_group_9.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_9_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_9_fill_1.property('Opacity').setValue(100);
var layer_7_group_9_transform = layer_7_group_9.property('Transform');
layer_7_group_9_transform.property('Position').setValue([146.43, 31.348]);
layer_7_group_9_transform.property('Scale').setValue([100, 100]);
layer_7_group_9_transform.property('Rotation').setValue(0);
layer_7_group_9_transform.property('Opacity').setValue(100);
// Shape Group: Group 11
var layer_7_group_10 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_10.name = 'Group 11';
var layer_7_group_10_path_0 = layer_7_group_10.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[5.723, 3.364], [-5.253, -4.36], [-5.519, -3.395], [5.016, 4.072]];
pathShape.inTangents = [[0.4, 0.497], [4.296, 1.499], [-0.604, -0.211], [-2.743, -3.414]];
pathShape.outTangents = [[-2.852, -3.549], [-0.608, -0.213], [4.138, 1.445], [0.403, 0.501]];
pathShape.closed = true;
layer_7_group_10_path_0.property('Path').setValue(pathShape);
var layer_7_group_10_fill_1 = layer_7_group_10.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_10_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_10_fill_1.property('Opacity').setValue(100);
var layer_7_group_10_transform = layer_7_group_10.property('Transform');
layer_7_group_10_transform.property('Position').setValue([148.309, 23.222]);
layer_7_group_10_transform.property('Scale').setValue([100, 100]);
layer_7_group_10_transform.property('Rotation').setValue(0);
layer_7_group_10_transform.property('Opacity').setValue(100);
// Shape Group: Group 12
var layer_7_group_11 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_11.name = 'Group 12';
var layer_7_group_11_path_0 = layer_7_group_11.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-5.85, -2.231], [5.463, 3.185], [5.729, 2.221], [-5.143, -2.938]];
pathShape.inTangents = [[-0.507, -0.396], [-4.127, -0.951], [0.628, 0.145], [3.196, 2.497]];
pathShape.outTangents = [[3.335, 2.605], [0.627, 0.144], [-3.95, -0.909], [-0.501, -0.392]];
pathShape.closed = true;
layer_7_group_11_path_0.property('Path').setValue(pathShape);
var layer_7_group_11_fill_1 = layer_7_group_11.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_11_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_11_fill_1.property('Opacity').setValue(100);
var layer_7_group_11_transform = layer_7_group_11.property('Transform');
layer_7_group_11_transform.property('Position').setValue([170.142, 44.349]);
layer_7_group_11_transform.property('Scale').setValue([100, 100]);
layer_7_group_11_transform.property('Rotation').setValue(0);
layer_7_group_11_transform.property('Opacity').setValue(100);
// Shape Group: Group 13
var layer_7_group_12 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_12.name = 'Group 13';
var layer_7_group_12_path_0 = layer_7_group_12.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-6.241, 0.344], [5.984, 0.906], [6.25, -0.057], [-6.241, -0.656]];
pathShape.inTangents = [[-0.642, 0.056], [-4.033, -0.739], [0.633, 0.115], [4.175, -0.366]];
pathShape.outTangents = [[4.083, -0.358], [0.629, 0.115], [-4.122, -0.755], [-0.636, 0.056]];
pathShape.closed = true;
layer_7_group_12_path_0.property('Path').setValue(pathShape);
var layer_7_group_12_fill_1 = layer_7_group_12.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_12_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_12_fill_1.property('Opacity').setValue(100);
var layer_7_group_12_transform = layer_7_group_12.property('Transform');
layer_7_group_12_transform.property('Position').setValue([173.357, 34.685]);
layer_7_group_12_transform.property('Scale').setValue([100, 100]);
layer_7_group_12_transform.property('Rotation').setValue(0);
layer_7_group_12_transform.property('Opacity').setValue(100);
// Shape Group: Group 14
var layer_7_group_13 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_13.name = 'Group 14';
var layer_7_group_13_path_0 = layer_7_group_13.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-6.866, 2.555], [7.296, -1.797], [7.296, -2.797], [-7.371, 1.691]];
pathShape.inTangents = [[-0.567, 0.306], [-4.957, 0.495], [0.641, -0.064], [4.546, -2.457]];
pathShape.outTangents = [[4.381, -2.369], [0.634, -0.063], [-5.14, 0.513], [-0.566, 0.306]];
pathShape.closed = true;
layer_7_group_13_path_0.property('Path').setValue(pathShape);
var layer_7_group_13_fill_1 = layer_7_group_13.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_13_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_13_fill_1.property('Opacity').setValue(100);
var layer_7_group_13_transform = layer_7_group_13.property('Transform');
layer_7_group_13_transform.property('Position').setValue([173.374, 28.747]);
layer_7_group_13_transform.property('Scale').setValue([100, 100]);
layer_7_group_13_transform.property('Rotation').setValue(0);
layer_7_group_13_transform.property('Opacity').setValue(100);
// Shape Group: Group 15
var layer_7_group_14 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_14.name = 'Group 15';
var layer_7_group_14_path_0 = layer_7_group_14.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-0.345, -6.388], [-2.019, 6.028], [-1.312, 6.734], [0.619, -6.654]];
pathShape.inTangents = [[-0.241, -0.598], [2.715, -3.489], [-0.394, 0.508], [1.79, 4.436]];
pathShape.outTangents = [[1.646, 4.08], [-0.39, 0.502], [2.925, -3.759], [-0.238, -0.589]];
pathShape.closed = true;
layer_7_group_14_path_0.property('Path').setValue(pathShape);
var layer_7_group_14_fill_1 = layer_7_group_14.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_14_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_14_fill_1.property('Opacity').setValue(100);
var layer_7_group_14_transform = layer_7_group_14.property('Transform');
layer_7_group_14_transform.property('Position').setValue([162.649, 36.368]);
layer_7_group_14_transform.property('Scale').setValue([100, 100]);
layer_7_group_14_transform.property('Rotation').setValue(0);
layer_7_group_14_transform.property('Opacity').setValue(100);
// Shape Group: Group 16
var layer_7_group_15 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_15.name = 'Group 16';
var layer_7_group_15_path_0 = layer_7_group_15.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[1.982, -6.442], [-1.538, 6.24], [-0.573, 5.974], [2.689, -5.734]];
pathShape.inTangents = [[0.5, -0.396], [-1.655, -4.418], [0.226, 0.605], [-3.45, 2.733]];
pathShape.outTangents = [[-3.7, 2.931], [0.224, 0.597], [-1.545, -4.124], [0.504, -0.399]];
pathShape.closed = true;
layer_7_group_15_path_0.property('Path').setValue(pathShape);
var layer_7_group_15_fill_1 = layer_7_group_15.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_15_fill_1.property('Color').setValue([0.937255, 0.294118, 0.435294]);
layer_7_group_15_fill_1.property('Opacity').setValue(100);
var layer_7_group_15_transform = layer_7_group_15.property('Transform');
layer_7_group_15_transform.property('Position').setValue([155.301, 34.973]);
layer_7_group_15_transform.property('Scale').setValue([100, 100]);
layer_7_group_15_transform.property('Rotation').setValue(0);
layer_7_group_15_transform.property('Opacity').setValue(100);
// Shape Group: Group 17
var layer_7_group_16 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_16.name = 'Group 17';
var layer_7_group_16_path_0 = layer_7_group_16.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[0.986, 10.305], [-33.245, 8.154], [-28.944, -15.502], [5.107, -1.165], [38.441, -5.286], [33.423, 22.313], [20.878, 24.105]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0], [0, 0], [-4.122, -10.932], [0, 0], [13.082, 6.451]];
pathShape.outTangents = [[-39.427, 15.233], [0, 0], [6.811, -15.054], [0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_16_path_0.property('Path').setValue(pathShape);
var layer_7_group_16_fill_1 = layer_7_group_16.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_16_fill_1.property('Color').setValue([0.960784, 0.396078, 0.494118]);
layer_7_group_16_fill_1.property('Opacity').setValue(100);
var layer_7_group_16_transform = layer_7_group_16.property('Transform');
layer_7_group_16_transform.property('Position').setValue([156.165, 30.806]);
layer_7_group_16_transform.property('Scale').setValue([100, 100]);
layer_7_group_16_transform.property('Rotation').setValue(0);
layer_7_group_16_transform.property('Opacity').setValue(100);
// Shape Group: Group 18
var layer_7_group_17 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_17.name = 'Group 18';
var layer_7_group_17_path_0 = layer_7_group_17.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[4.928, 0.896], [-1.434, 7.885], [-4.928, -0.896], [1.433, -7.885]];
pathShape.inTangents = [[0.792, -4.355], [2.722, 0.495], [-0.792, 4.355], [-2.721, -0.495]];
pathShape.outTangents = [[-0.792, 4.355], [-2.722, -0.494], [0.791, -4.355], [2.723, 0.494]];
pathShape.closed = true;
layer_7_group_17_path_0.property('Path').setValue(pathShape);
var layer_7_group_17_fill_1 = layer_7_group_17.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_17_fill_1.property('Color').setValue([0.960784, 0.396078, 0.494118]);
layer_7_group_17_fill_1.property('Opacity').setValue(100);
var layer_7_group_17_transform = layer_7_group_17.property('Transform');
layer_7_group_17_transform.property('Position').setValue([159.212, 35.376]);
layer_7_group_17_transform.property('Scale').setValue([100, 100]);
layer_7_group_17_transform.property('Rotation').setValue(0);
layer_7_group_17_transform.property('Opacity').setValue(100);
// Shape Group: Group 19
var layer_7_group_18 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_18.name = 'Group 19';
var layer_7_group_18_path_0 = layer_7_group_18.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-2.777, -25.09], [-7.438, 0.537], [2.419, 2.33], [7.438, -25.269]];
pathShape.inTangents = [[0, 0], [0, 0], [-8.244, 22.939], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_7_group_18_path_0.property('Path').setValue(pathShape);
var layer_7_group_18_fill_1 = layer_7_group_18.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_18_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_7_group_18_fill_1.property('Opacity').setValue(100);
var layer_7_group_18_transform = layer_7_group_18.property('Transform');
layer_7_group_18_transform.property('Position').setValue([129.147, 64.266]);
layer_7_group_18_transform.property('Scale').setValue([100, 100]);
layer_7_group_18_transform.property('Rotation').setValue(0);
layer_7_group_18_transform.property('Opacity').setValue(100);
// Shape Group: Group 20
var layer_7_group_19 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_19.name = 'Group 20';
var layer_7_group_19_path_0 = layer_7_group_19.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-0.448, -21.596], [-3.674, 18.549], [9.051, -17.831]];
pathShape.inTangents = [[0, 0], [-5.556, -3.046], [0.179, 10.215]];
pathShape.outTangents = [[0, 0], [5.555, 3.047], [0, 0]];
pathShape.closed = true;
layer_7_group_19_path_0.property('Path').setValue(pathShape);
var layer_7_group_19_fill_1 = layer_7_group_19.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_19_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_7_group_19_fill_1.property('Opacity').setValue(100);
var layer_7_group_19_transform = layer_7_group_19.property('Transform');
layer_7_group_19_transform.property('Position').setValue([172.517, 67.044]);
layer_7_group_19_transform.property('Scale').setValue([100, 100]);
layer_7_group_19_transform.property('Rotation').setValue(0);
layer_7_group_19_transform.property('Opacity').setValue(100);
// Shape Group: Group 21
var layer_7_group_20 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_20.name = 'Group 21';
var layer_7_group_20_path_0 = layer_7_group_20.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-2.777, -24.104], [-3.136, 22.671], [11.022, -21.595]];
pathShape.inTangents = [[0, 0], [-7.886, -1.434], [0, 0]];
pathShape.outTangents = [[0, 0], [7.885, 1.433], [0, 0]];
pathShape.closed = true;
layer_7_group_20_path_0.property('Path').setValue(pathShape);
var layer_7_group_20_fill_1 = layer_7_group_20.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_20_fill_1.property('Color').setValue([0.666667, 0.411765, 0.286275]);
layer_7_group_20_fill_1.property('Opacity').setValue(100);
var layer_7_group_20_transform = layer_7_group_20.property('Transform');
layer_7_group_20_transform.property('Position').setValue([149.577, 62.922]);
layer_7_group_20_transform.property('Scale').setValue([100, 100]);
layer_7_group_20_transform.property('Rotation').setValue(0);
layer_7_group_20_transform.property('Opacity').setValue(100);
// Shape Group: Group 22
var layer_7_group_21 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_21.name = 'Group 22';
var layer_7_group_21_path_0 = layer_7_group_21.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[97.023, 51.973], [20.642, 83.28], [-84.267, 42.682], [-108.073, -8.993], [2.726, -87.844], [54.194, -67.725], [91.235, -22.251]];
pathShape.inTangents = [[23.72, -32.273], [62.305, -8.04], [0, 0], [0, 0], [0, 0], [-24.786, -21.723], [0, 0]];
pathShape.outTangents = [[0, 0], [-62.305, 8.04], [-36.476, -36.762], [44.338, -82.327], [0, 0], [20.624, 18.075], [0, 0]];
pathShape.closed = true;
layer_7_group_21_path_0.property('Path').setValue(pathShape);
var layer_7_group_21_fill_1 = layer_7_group_21.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_21_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_7_group_21_fill_1.property('Opacity').setValue(100);
var layer_7_group_21_transform = layer_7_group_21.property('Transform');
layer_7_group_21_transform.property('Position').setValue([140.629, 126.51]);
layer_7_group_21_transform.property('Scale').setValue([100, 100]);
layer_7_group_21_transform.property('Rotation').setValue(0);
layer_7_group_21_transform.property('Opacity').setValue(100);
// Shape Group: Group 23
var layer_7_group_22 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_22.name = 'Group 23';
var layer_7_group_22_path_0 = layer_7_group_22.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[36.216, -35.283], [7.6, -15.68], [0.972, -8.062], [8.598, -2.424], [3.939, 0.802], [9.136, 5.82], [4.118, 11.018], [2.146, 33.062], [4.341, 35.097], [-34.411, 9.633], [-23.021, -19.054], [-15.763, -22.466], [14.011, -33.87], [25.316, -37.268], [36.012, -36.102]];
pathShape.inTangents = [[0.324, -0.242], [0, 0], [1.559, -3.027], [-10.322, 3.011], [0, 0], [-10.574, 2.15], [0, 0], [-14.337, -10.753], [0, 0], [0.539, 30.157], [-13.519, 7.026], [0, 0], [-10.191, 3.064], [0, 0], [-3.309, -3.687]];
pathShape.outTangents = [[0, 0], [-2.731, 2.033], [-2.116, 4.112], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.631, -4.529], [0, 0], [0, 0], [0.269, 0.301]];
pathShape.closed = true;
layer_7_group_22_path_0.property('Path').setValue(pathShape);
var layer_7_group_22_fill_1 = layer_7_group_22.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_22_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_7_group_22_fill_1.property('Opacity').setValue(100);
var layer_7_group_22_transform = layer_7_group_22.property('Transform');
layer_7_group_22_transform.property('Position').setValue([216.273, 69.085]);
layer_7_group_22_transform.property('Scale').setValue([100, 100]);
layer_7_group_22_transform.property('Rotation').setValue(0);
layer_7_group_22_transform.property('Opacity').setValue(100);
// Shape Group: Group 24
var layer_7_group_23 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_23.name = 'Group 24';
var layer_7_group_23_path_0 = layer_7_group_23.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[7.146, 34.426], [34.208, -36.005], [20.05, -36.543], [-23.028, -20.698], [-35.908, -4.898]];
pathShape.inTangents = [[-46.908, 6.237], [5.555, 70.252], [10.933, -4.121], [16.446, -7.472], [1.55, -6.987]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [-6.515, 2.96], [-3.655, 16.47]];
pathShape.closed = true;
layer_7_group_23_path_0.property('Path').setValue(pathShape);
var layer_7_group_23_fill_1 = layer_7_group_23.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_23_fill_1.property('Color').setValue([0.874510, 0.392157, 0.372549]);
layer_7_group_23_fill_1.property('Opacity').setValue(100);
var layer_7_group_23_transform = layer_7_group_23.property('Transform');
layer_7_group_23_transform.property('Position').setValue([219.159, 69.154]);
layer_7_group_23_transform.property('Scale').setValue([100, 100]);
layer_7_group_23_transform.property('Rotation').setValue(0);
layer_7_group_23_transform.property('Opacity').setValue(100);
// Shape Group: Group 25
var layer_7_group_24 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_24.name = 'Group 25';
var layer_7_group_24_path_0 = layer_7_group_24.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-29.847, -45.979], [-9.963, -17.558], [-6.441, -8.094], [-15.564, -5.5], [-12.338, -0.841], [-18.969, 2.026], [-16.102, 8.658], [-22.016, 29.984], [-24.787, 31.117], [20.448, 20.924], [19.886, -9.936], [14.293, -15.686], [-9.561, -36.841], [-18.945, -44], [-29.367, -46.674]];
pathShape.inTangents = [[-0.219, -0.341], [0, 0], [-0.393, -3.382], [8.602, 6.452], [0, 0], [9.14, 5.735], [0, 0], [17.204, -5.018], [0, 0], [-11.121, 28.037], [10.18, 11.335], [0, 0], [8.461, 6.455], [0, 0], [4.395, -2.286]];
pathShape.outTangents = [[0, 0], [1.841, 2.865], [0.533, 4.594], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-7.42, -7.628], [0, 0], [0, 0], [-0.359, 0.187]];
pathShape.closed = true;
layer_7_group_24_path_0.property('Path').setValue(pathShape);
var layer_7_group_24_fill_1 = layer_7_group_24.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_24_fill_1.property('Color').setValue([0.839216, 0.603922, 0.400000]);
layer_7_group_24_fill_1.property('Opacity').setValue(100);
var layer_7_group_24_transform = layer_7_group_24.property('Transform');
layer_7_group_24_transform.property('Position').setValue([93.412, 51.539]);
layer_7_group_24_transform.property('Scale').setValue([100, 100]);
layer_7_group_24_transform.property('Rotation').setValue(0);
layer_7_group_24_transform.property('Opacity').setValue(100);
// Shape Group: Group 26
var layer_7_group_25 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_25.name = 'Group 26';
var layer_7_group_25_path_0 = layer_7_group_25.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-12.342, 28.162], [-12.879, -47.286], [0.562, -42.807], [35.305, -12.812], [41.8, 6.51]];
pathShape.inTangents = [[41.71, 22.35], [-29.929, 63.8], [-8.781, -7.706], [-12.763, -12.783], [1.008, -7.085]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [5.057, 5.065], [-2.376, 16.702]];
pathShape.closed = true;
layer_7_group_25_path_0.property('Path').setValue(pathShape);
var layer_7_group_25_fill_1 = layer_7_group_25.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_25_fill_1.property('Color').setValue([0.874510, 0.392157, 0.372549]);
layer_7_group_25_fill_1.property('Opacity').setValue(100);
var layer_7_group_25_transform = layer_7_group_25.property('Transform');
layer_7_group_25_transform.property('Position').setValue([75.852, 51.927]);
layer_7_group_25_transform.property('Scale').setValue([100, 100]);
layer_7_group_25_transform.property('Rotation').setValue(0);
layer_7_group_25_transform.property('Opacity').setValue(100);
layer_7.inPoint = 0.000000;
layer_7.outPoint = 6.250000;
layer_7.startTime = 0.000000;
layer_7.parent = layer_9;
layer_7.property('Transform').property('Opacity').setValue(100);
// Smart detection: 7 keyframes for layer_7.property('Transform').property('Position')
layer_7.property('Transform').property('Position').setValueAtTime(0/frameRate, [31.219, 23.645, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(25/frameRate, [31.219, 15.953, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(50/frameRate, [61.988, 15.953, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(75/frameRate, [31.219, 15.953, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(100/frameRate, [31.219, 15.953, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(125/frameRate, [61.988, 15.953, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(149/frameRate, [31.219, 23.645, 0]);
layer_7.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 7 keyframes for layer_7.property('Transform').property('Rotation')
layer_7.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 20.256);
layer_7.property('Transform').property('Rotation').setValueAtTime(25/frameRate, 4);
layer_7.property('Transform').property('Rotation').setValueAtTime(50/frameRate, -39.515);
layer_7.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -2.93);
layer_7.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 4);
layer_7.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -39.515);
layer_7.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 20.256);
layer_7.property('Transform').property('Anchor Point').setValue([143.993, 207.67, 0]);

// Layer 4: L Eyses
var layer_4 = createShapeLayer(comp, 'L Eyses');
// Shape Group: Group 1
var layer_4_group_0 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_0.name = 'Group 1';
var layer_4_group_0_path_0 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.326, 3.236], [-4.882, 3.506], [-2.326, -3.238], [4.882, -3.508]];
pathShape.inTangents = [[2.696, -1.937], [1.285, 1.788], [-2.696, 1.937], [-1.284, -1.787]];
pathShape.outTangents = [[-2.696, 1.937], [-1.285, -1.788], [2.696, -1.936], [1.285, 1.788]];
pathShape.closed = true;
layer_4_group_0_path_0.property('Path').setValue(pathShape);
var layer_4_group_0_fill_1 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_4_group_0_fill_1.property('Opacity').setValue(100);
var layer_4_group_0_transform = layer_4_group_0.property('Transform');
layer_4_group_0_transform.property('Position').setValue([28.198, 21.826]);
layer_4_group_0_transform.property('Scale').setValue([100, 100]);
layer_4_group_0_transform.property('Rotation').setValue(0);
layer_4_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_4_group_1 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_1.name = 'Group 2';
var layer_4_group_1_path_0 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[6.412, -0.276], [-2.29, -8.423], [3.411, 2.681], [-6.412, 3.047], [0.757, 8.423]];
pathShape.inTangents = [[0, 0], [0, 0], [-4.782, -1.103], [0, 0], [-5.914, -1.075]];
pathShape.outTangents = [[-5.994, -0.635], [0, 0], [2.364, 0.545], [0, 0], [0, 0]];
pathShape.closed = false;
layer_4_group_1_path_0.property('Path').setValue(pathShape);
var layer_4_group_1_fill_1 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_1_fill_1.property('Color').setValue([0.007843, 0.000000, 0.015686]);
layer_4_group_1_fill_1.property('Opacity').setValue(100);
var layer_4_group_1_transform = layer_4_group_1.property('Transform');
layer_4_group_1_transform.property('Position').setValue([6.662, 13.317]);
layer_4_group_1_transform.property('Scale').setValue([100, 100]);
layer_4_group_1_transform.property('Rotation').setValue(0);
layer_4_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_4_group_2 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_2.name = 'Group 3';
var layer_4_group_2_path_0 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[19.714, 3.584], [-3.554, 19.542], [-19.713, -3.584], [3.554, -19.541]];
pathShape.inTangents = [[1.962, -10.792], [10.888, 1.979], [-1.962, 10.792], [-10.888, -1.98]];
pathShape.outTangents = [[-1.962, 10.792], [-10.887, -1.98], [1.963, -10.792], [10.888, 1.979]];
pathShape.closed = true;
layer_4_group_2_path_0.property('Path').setValue(pathShape);
var layer_4_group_2_fill_1 = layer_4_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_2_fill_1.property('Color').setValue([0.003922, 0.000000, 0.007843]);
layer_4_group_2_fill_1.property('Opacity').setValue(100);
var layer_4_group_2_transform = layer_4_group_2.property('Transform');
layer_4_group_2_transform.property('Position').setValue([33.615, 34.471]);
layer_4_group_2_transform.property('Scale').setValue([100, 100]);
layer_4_group_2_transform.property('Rotation').setValue(0);
layer_4_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_4_group_3 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_3.name = 'Group 4';
var layer_4_group_3_path_0 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[22.671, 4.122], [-4.122, 22.67], [-22.67, -4.122], [4.123, -22.67]];
pathShape.inTangents = [[2.276, -12.521], [12.521, 2.277], [-2.277, 12.52], [-12.521, -2.277]];
pathShape.outTangents = [[-2.277, 12.521], [-12.52, -2.276], [2.276, -12.521], [12.52, 2.276]];
pathShape.closed = true;
layer_4_group_3_path_0.property('Path').setValue(pathShape);
var layer_4_group_3_fill_1 = layer_4_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_3_fill_1.property('Color').setValue([0.690196, 0.800000, 0.662745]);
layer_4_group_3_fill_1.property('Opacity').setValue(100);
var layer_4_group_3_transform = layer_4_group_3.property('Transform');
layer_4_group_3_transform.property('Position').setValue([33.136, 31.507]);
layer_4_group_3_transform.property('Scale').setValue([100, 100]);
layer_4_group_3_transform.property('Rotation').setValue(0);
layer_4_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_4_group_4 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_4.name = 'Group 5';
var layer_4_group_4_path_0 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[26.613, 4.839], [-4.839, 26.613], [-26.614, -4.839], [4.839, -26.613]];
pathShape.inTangents = [[2.673, -14.698], [14.698, 2.673], [-2.672, 14.698], [-14.698, -2.673]];
pathShape.outTangents = [[-2.672, 14.698], [-14.698, -2.672], [2.673, -14.698], [14.698, 2.672]];
pathShape.closed = true;
layer_4_group_4_path_0.property('Path').setValue(pathShape);
var layer_4_group_4_fill_1 = layer_4_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_4_fill_1.property('Color').setValue([0.007843, 0.000000, 0.015686]);
layer_4_group_4_fill_1.property('Opacity').setValue(100);
var layer_4_group_4_transform = layer_4_group_4.property('Transform');
layer_4_group_4_transform.property('Position').setValue([33.495, 29.536]);
layer_4_group_4_transform.property('Scale').setValue([100, 100]);
layer_4_group_4_transform.property('Rotation').setValue(0);
layer_4_group_4_transform.property('Opacity').setValue(100);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 6.250000;
layer_4.startTime = 0.000000;
layer_4.parent = layer_7;
layer_4.property('Transform').property('Opacity').setValue(100);
layer_4.property('Transform').property('Position').setValue([85.097, 124.086, 0]);
// Smart detection: 45 keyframes for layer_4.property('Transform').property('Scale')
layer_4.property('Transform').property('Scale').setValueAtTime(30/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(31/frameRate, [100, 94.461, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(32/frameRate, [100, 80.175, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(33/frameRate, [100, 60.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(34/frameRate, [100, 39.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(35/frameRate, [100, 19.825, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 5.539, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(37/frameRate, [100, 0, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(38/frameRate, [100, 4.297, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(39/frameRate, [100, 15.625, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 31.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(42/frameRate, [100, 68.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 84.375, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(44/frameRate, [100, 95.703, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(45/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(75/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(76/frameRate, [100, 94.461, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(77/frameRate, [100, 80.175, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(78/frameRate, [100, 60.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(79/frameRate, [100, 39.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(80/frameRate, [100, 19.825, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(81/frameRate, [100, 5.539, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(82/frameRate, [100, 0, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(83/frameRate, [100, 4.297, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(84/frameRate, [100, 15.625, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(85/frameRate, [100, 31.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(87/frameRate, [100, 68.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(88/frameRate, [100, 84.375, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(89/frameRate, [100, 95.703, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(90/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(120/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(121/frameRate, [100, 94.461, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(122/frameRate, [100, 80.175, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(123/frameRate, [100, 60.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(124/frameRate, [100, 39.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(125/frameRate, [100, 19.825, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(126/frameRate, [100, 5.539, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(127/frameRate, [100, 0, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(128/frameRate, [100, 4.297, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(129/frameRate, [100, 15.625, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(130/frameRate, [100, 31.641, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(132/frameRate, [100, 68.359, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(133/frameRate, [100, 84.375, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(134/frameRate, [100, 95.703, 100]);
layer_4.property('Transform').property('Scale').setValueAtTime(135/frameRate, [100, 100, 100]);
layer_4.property('Transform').property('Rotation').setValue(0);
layer_4.property('Transform').property('Anchor Point').setValue([31.515, 29.536, 0]);

// Layer 5: R Eyes
var layer_5 = createShapeLayer(comp, 'R Eyes');
// Shape Group: Group 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Group 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[2.326, 3.236], [-4.882, 3.506], [-2.326, -3.238], [4.882, -3.508]];
pathShape.inTangents = [[2.696, -1.937], [1.284, 1.788], [-2.696, 1.937], [-1.285, -1.787]];
pathShape.outTangents = [[-2.697, 1.937], [-1.285, -1.788], [2.696, -1.936], [1.285, 1.788]];
pathShape.closed = true;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_fill_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_5_group_0_fill_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([21.909, 23.439]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_5_group_1 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_1.name = 'Group 2';
var layer_5_group_1_path_0 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-5.187, -3.323], [5.824, -7.885], [-3.42, 0.501], [5.645, 4.302], [-2.957, 6.81]];
pathShape.inTangents = [[0, 0], [0, 0], [4.864, 0.651], [0, 0], [5.915, 1.075]];
pathShape.outTangents = [[5.834, 1.516], [0, 0], [-2.405, -0.322], [0, 0], [0, 0]];
pathShape.closed = false;
layer_5_group_1_path_0.property('Path').setValue(pathShape);
var layer_5_group_1_fill_1 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_1_fill_1.property('Color').setValue([0.007843, 0.000000, 0.015686]);
layer_5_group_1_fill_1.property('Opacity').setValue(100);
var layer_5_group_1_transform = layer_5_group_1.property('Transform');
layer_5_group_1_transform.property('Position').setValue([59.644, 24.608]);
layer_5_group_1_transform.property('Scale').setValue([100, 100]);
layer_5_group_1_transform.property('Rotation').setValue(0);
layer_5_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_5_group_2 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_2.name = 'Group 3';
var layer_5_group_2_path_0 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-19.714, -3.585], [-3.553, 19.54], [19.713, 3.584], [3.553, -19.542]];
pathShape.inTangents = [[1.962, -10.792], [-10.888, -1.979], [-1.962, 10.792], [10.887, 1.98]];
pathShape.outTangents = [[-1.962, 10.792], [10.887, 1.98], [1.963, -10.792], [-10.888, -1.979]];
pathShape.closed = true;
layer_5_group_2_path_0.property('Path').setValue(pathShape);
var layer_5_group_2_fill_1 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_2_fill_1.property('Color').setValue([0.003922, 0.000000, 0.007843]);
layer_5_group_2_fill_1.property('Opacity').setValue(100);
var layer_5_group_2_transform = layer_5_group_2.property('Transform');
layer_5_group_2_transform.property('Position').setValue([27.685, 34.113]);
layer_5_group_2_transform.property('Scale').setValue([100, 100]);
layer_5_group_2_transform.property('Rotation').setValue(0);
layer_5_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_5_group_3 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_3.name = 'Group 4';
var layer_5_group_3_path_0 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-22.67, -4.122], [-4.123, 22.671], [22.67, 4.123], [4.123, -22.67]];
pathShape.inTangents = [[2.276, -12.521], [-12.52, -2.277], [-2.277, 12.52], [12.52, 2.277]];
pathShape.outTangents = [[-2.277, 12.521], [12.521, 2.276], [2.277, -12.521], [-12.521, -2.277]];
pathShape.closed = true;
layer_5_group_3_path_0.property('Path').setValue(pathShape);
var layer_5_group_3_fill_1 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_3_fill_1.property('Color').setValue([0.690196, 0.800000, 0.662745]);
layer_5_group_3_fill_1.property('Opacity').setValue(100);
var layer_5_group_3_transform = layer_5_group_3.property('Transform');
layer_5_group_3_transform.property('Position').setValue([29.177, 31.507]);
layer_5_group_3_transform.property('Scale').setValue([100, 100]);
layer_5_group_3_transform.property('Rotation').setValue(0);
layer_5_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_5_group_4 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_4.name = 'Group 5';
var layer_5_group_4_path_0 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-26.613, -4.839], [-4.839, 26.613], [26.613, 4.839], [4.84, -26.613]];
pathShape.inTangents = [[2.673, -14.698], [-14.698, -2.672], [-2.673, 14.698], [14.698, 2.672]];
pathShape.outTangents = [[-2.672, 14.698], [14.699, 2.673], [2.672, -14.698], [-14.699, -2.673]];
pathShape.closed = true;
layer_5_group_4_path_0.property('Path').setValue(pathShape);
var layer_5_group_4_fill_1 = layer_5_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_4_fill_1.property('Color').setValue([0.007843, 0.000000, 0.015686]);
layer_5_group_4_fill_1.property('Opacity').setValue(100);
var layer_5_group_4_transform = layer_5_group_4.property('Transform');
layer_5_group_4_transform.property('Position').setValue([29.535, 29.536]);
layer_5_group_4_transform.property('Scale').setValue([100, 100]);
layer_5_group_4_transform.property('Rotation').setValue(0);
layer_5_group_4_transform.property('Opacity').setValue(100);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 6.250000;
layer_5.startTime = 0.000000;
layer_5.parent = layer_7;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([195.24, 141.113, 0]);
// Smart detection: 45 keyframes for layer_5.property('Transform').property('Scale')
layer_5.property('Transform').property('Scale').setValueAtTime(30/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(31/frameRate, [100, 94.461, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(32/frameRate, [100, 80.175, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(33/frameRate, [100, 60.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(34/frameRate, [100, 39.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(35/frameRate, [100, 19.825, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 5.539, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(37/frameRate, [100, 0, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(38/frameRate, [100, 4.297, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(39/frameRate, [100, 15.625, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 31.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(42/frameRate, [100, 68.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 84.375, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(44/frameRate, [100, 95.703, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(45/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(75/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(76/frameRate, [100, 94.461, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(77/frameRate, [100, 80.175, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(78/frameRate, [100, 60.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(79/frameRate, [100, 39.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(80/frameRate, [100, 19.825, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(81/frameRate, [100, 5.539, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(82/frameRate, [100, 0, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(83/frameRate, [100, 4.297, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(84/frameRate, [100, 15.625, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(85/frameRate, [100, 31.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(87/frameRate, [100, 68.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(88/frameRate, [100, 84.375, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(89/frameRate, [100, 95.703, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(90/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(120/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(121/frameRate, [100, 94.461, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(122/frameRate, [100, 80.175, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(123/frameRate, [100, 60.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(124/frameRate, [100, 39.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(125/frameRate, [100, 19.825, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(126/frameRate, [100, 5.539, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(127/frameRate, [100, 0, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(128/frameRate, [100, 4.297, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(129/frameRate, [100, 15.625, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(130/frameRate, [100, 31.641, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(132/frameRate, [100, 68.359, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(133/frameRate, [100, 84.375, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(134/frameRate, [100, 95.703, 100]);
layer_5.property('Transform').property('Scale').setValueAtTime(135/frameRate, [100, 100, 100]);
layer_5.property('Transform').property('Rotation').setValue(0);
layer_5.property('Transform').property('Anchor Point').setValue([32.859, 29.536, 0]);

// Layer 6: Mouth
var layer_6 = createShapeLayer(comp, 'Mouth');
// Shape Group: Group 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Group 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-22.67, -7.214], [2.42, -10.798], [22.67, 1.03]];
pathShape.inTangents = [[0, 0], [-12.634, 19.086], [-19.086, 9.767]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.closed = false;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_stroke_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Stroke');
layer_6_group_0_stroke_1.property('Color').setValue([0.003922, 0.000000, 0.007843]);
layer_6_group_0_stroke_1.property('Stroke Width').setValue(1);
layer_6_group_0_stroke_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([25.17, 13.298]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_6_group_1 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_1.name = 'Group 2';
var layer_6_group_1_path_0 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-10.264, -4.581], [10.264, -0.848], [3.18, -14.606]];
pathShape.inTangents = [[4.477, 1.064], [-14.432, 15.454], [0.538, 8.244]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_6_group_1_path_0.property('Path').setValue(pathShape);
var layer_6_group_1_fill_1 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_1_fill_1.property('Color').setValue([0.901961, 0.341176, 0.462745]);
layer_6_group_1_fill_1.property('Opacity').setValue(100);
var layer_6_group_1_transform = layer_6_group_1.property('Transform');
layer_6_group_1_transform.property('Position').setValue([24.41, 17.106]);
layer_6_group_1_transform.property('Scale').setValue([100, 100]);
layer_6_group_1_transform.property('Rotation').setValue(0);
layer_6_group_1_transform.property('Opacity').setValue(100);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 6.250000;
layer_6.startTime = 0.000000;
layer_6.parent = layer_7;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([136.629, 165.346, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_6.property('Transform').property('Rotation').setValue(0);
layer_6.property('Transform').property('Anchor Point').setValue([25.171, 15.981, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "01" created successfully!\n' +
      'Duration: 6.00 seconds\n' +
      'Layers: 14\n' +
      'Assets: 0');

app.endUndoGroup();
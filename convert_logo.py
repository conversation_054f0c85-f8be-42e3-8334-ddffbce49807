#!/usr/bin/env python3
"""
Simple script to convert logo.json to JSX
"""

from lottie_to_jsx_converter import LottieToJSXConverter

def main():
    # Input and output files
    input_file = "logo.json"
    output_file = "logo_animation_complete.jsx"
    
    print("🚀 Starting Lottie to JSX conversion...")
    print(f"📁 Input:  {input_file}")
    print(f"📁 Output: {output_file}")
    print()
    
    try:
        # Create converter instance
        converter = LottieToJSXConverter()
        
        # Convert the file
        jsx_script = converter.convert_json_to_jsx(input_file, output_file)
        
        # Show results
        lines = jsx_script.count('\n') + 1
        print()
        print("✅ Conversion completed successfully!")
        print(f"📊 Generated JSX script: {lines:,} lines")
        print(f"💾 File saved: {output_file}")
        print()
        print("🎬 To use in After Effects:")
        print("   1. Open After Effects")
        print("   2. Go to File > Scripts > Run Script File...")
        print(f"   3. Select '{output_file}'")
        print("   4. Click 'Open' to run the script")
        print()
        print("🎉 Your complete logo animation will be created!")
        
    except FileNotFoundError:
        print(f"❌ Error: Could not find '{input_file}'")
        print("   Make sure the logo.json file is in the same directory.")
        
    except Exception as e:
        print(f"❌ Error during conversion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

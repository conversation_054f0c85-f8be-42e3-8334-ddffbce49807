import json

def analyze_cat_structure():
    with open('cat.json', 'r') as f:
        data = json.load(f)
    
    print("=== DEEP STRUCTURAL ANALYSIS ===")
    layers = data['layers']
    
    # Create hierarchy map
    hierarchy = {}
    for layer in layers:
        idx = layer.get('ind', 0)
        name = layer.get('nm', '')
        parent = layer.get('parent', None)
        hierarchy[idx] = {
            'name': name,
            'parent': parent,
            'children': [],
            'layer': layer
        }
    
    # Build parent-child relationships
    for idx, info in hierarchy.items():
        if info['parent']:
            if info['parent'] in hierarchy:
                hierarchy[info['parent']]['children'].append(idx)
    
    print("HIERARCHY TREE:")
    def print_tree(idx, level=0):
        if idx not in hierarchy:
            return
        info = hierarchy[idx]
        indent = "  " * level
        print(f"{indent}├─ {idx}: {info['name']}")
        for child in sorted(info['children']):
            print_tree(child, level + 1)
    
    # Find root layers (no parent)
    roots = [idx for idx, info in hierarchy.items() if not info['parent']]
    for root in sorted(roots):
        print_tree(root)
    
    print("\n=== VISUAL DEPTH ANALYSIS ===")
    
    # Analyze by body part and position
    body_parts = {
        'background': [],
        'back_legs': [],
        'body_core': [],
        'front_legs': [],
        'head_face': [],
        'facial_features': []
    }
    
    for layer in layers:
        idx = layer.get('ind', 0)
        name = layer.get('nm', '').lower()
        parent = layer.get('parent', None)
        
        # Detailed classification
        if 'r b' in name or 'right back' in name or (name.startswith('r ') and 'b' in name):
            body_parts['back_legs'].append((idx, name, parent, 'right_back'))
        elif 'l b' in name or 'left back' in name or ('l ' in name and 'b' in name):
            body_parts['back_legs'].append((idx, name, parent, 'left_back'))
        elif 'r f' in name or 'right front' in name or (name.startswith('r ') and 'f' in name):
            body_parts['front_legs'].append((idx, name, parent, 'right_front'))
        elif 'l f' in name or 'left front' in name or ('l ' in name and 'f' in name):
            body_parts['front_legs'].append((idx, name, parent, 'left_front'))
        elif name == 'body' or name == 'tail':
            body_parts['body_core'].append((idx, name, parent, 'core'))
        elif name == 'face':
            body_parts['head_face'].append((idx, name, parent, 'face'))
        elif any(x in name for x in ['eye', 'mouth']):
            body_parts['facial_features'].append((idx, name, parent, 'feature'))
        else:
            # Check parent relationships for classification
            if parent == 1:  # R F Thigh children
                body_parts['front_legs'].append((idx, name, parent, 'right_front_child'))
            elif parent == 2:  # l F Thigh children  
                body_parts['front_legs'].append((idx, name, parent, 'left_front_child'))
            elif parent == 7:  # Face children
                body_parts['facial_features'].append((idx, name, parent, 'face_child'))
            elif parent == 9:  # Body children
                body_parts['body_core'].append((idx, name, parent, 'body_child'))
            elif parent == 11:  # L Thigh children
                body_parts['back_legs'].append((idx, name, parent, 'left_back_child'))
            elif parent == 12:  # R B Thigh children
                body_parts['back_legs'].append((idx, name, parent, 'right_back_child'))
            else:
                body_parts['body_core'].append((idx, name, parent, 'unclassified'))
    
    print("OPTIMAL RENDER ORDER (back to front):")
    order = 1
    
    print(f"\n{order}. BACK LEGS (furthest back):")
    for idx, name, parent, subtype in sorted(body_parts['back_legs']):
        print(f"   {idx:2d}: {name:15s} (parent: {parent}) [{subtype}]")
        order += 1
    
    print(f"\n{order}. BODY CORE (middle layer):")
    for idx, name, parent, subtype in sorted(body_parts['body_core']):
        print(f"   {idx:2d}: {name:15s} (parent: {parent}) [{subtype}]")
        order += 1
        
    print(f"\n{order}. FRONT LEGS (in front of body):")
    for idx, name, parent, subtype in sorted(body_parts['front_legs']):
        print(f"   {idx:2d}: {name:15s} (parent: {parent}) [{subtype}]")
        order += 1
        
    print(f"\n{order}. HEAD/FACE (front layer):")
    for idx, name, parent, subtype in sorted(body_parts['head_face']):
        print(f"   {idx:2d}: {name:15s} (parent: {parent}) [{subtype}]")
        order += 1
        
    print(f"\n{order}. FACIAL FEATURES (frontmost):")
    for idx, name, parent, subtype in sorted(body_parts['facial_features']):
        print(f"   {idx:2d}: {name:15s} (parent: {parent}) [{subtype}]")
        order += 1

if __name__ == "__main__":
    analyze_cat_structure()

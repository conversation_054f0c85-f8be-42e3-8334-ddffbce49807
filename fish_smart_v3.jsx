// 08 - Converted from <PERSON><PERSON> JSON
// Original Bodymovin version: 5.12.1
// Generated by <PERSON><PERSON> to JSX Converter

// Disable undo for performance
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            
            // Set easing if available
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

function createPrecomp(comp, name, refId) {
    // This would reference a precomp - simplified for now
    return createNullLayer(comp, name + ' (Precomp)');
}

function rgbToAE(r, g, b, a) {
    a = a || 1;
    return [r, g, b, a];
}

function bezierToAE(bezierData) {
    var shape = new Shape();
    if (bezierData.v && bezierData.v.length > 0) {
        shape.vertices = bezierData.v;
        if (bezierData.i) shape.inTangents = bezierData.i;
        if (bezierData.o) shape.outTangents = bezierData.o;
        shape.closed = bezierData.c || false;
    }
    return shape;
}

// Create main composition: 08
var comp = app.project.items.addComp('08', 1080, 1080, 1.0, 6.000, 25);
var frameRate = 25;

// Asset References and Precomps

// Creating 12 layers
// Layer 12: Bg
var layer_12 = createShapeLayer(comp, 'Bg');
// Shape Group: Group 1
var layer_12_group_0 = layer_12.property('Contents').addProperty('ADBE Vector Group');
layer_12_group_0.name = 'Group 1';
var layer_12_group_0_path_0 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-539.819, 540], [539.819, 540], [539.819, -540], [-539.819, -540]];
pathShape.inTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.outTangents = [[0, 0], [0, 0], [0, 0], [0, 0]];
pathShape.closed = true;
layer_12_group_0_path_0.property('Path').setValue(pathShape);
var layer_12_group_0_fill_1 = layer_12_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_12_group_0_fill_1.property('Color').setValue([0.156863, 0.431373, 0.552941]);
layer_12_group_0_fill_1.property('Opacity').setValue(100);
var layer_12_group_0_transform = layer_12_group_0.property('Transform');
layer_12_group_0_transform.property('Position').setValue([540.069, 540.25]);
layer_12_group_0_transform.property('Scale').setValue([100, 100]);
layer_12_group_0_transform.property('Rotation').setValue(0);
layer_12_group_0_transform.property('Opacity').setValue(100);
layer_12.inPoint = 0.000000;
layer_12.outPoint = 6.250000;
layer_12.startTime = 0.000000;
layer_12.property('Transform').property('Opacity').setValue(100);
layer_12.property('Transform').property('Position').setValue([540.181, 540, 0]);
layer_12.property('Transform').property('Scale').setValue([100, 100, 100]);
layer_12.property('Transform').property('Rotation').setValue(0);
layer_12.property('Transform').property('Anchor Point').setValue([540.069, 540.25, 0]);

// Layer 2: Body
var layer_2 = createShapeLayer(comp, 'Body');
// Shape Group: Group 1
var layer_2_group_0 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_0.name = 'Group 1';
var layer_2_group_0_path_0 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-36.419, 25.396], [36.419, -1.124], [-12.761, -18.71]];
pathShape.inTangents = [[2.841, -18.456], [-23.116, 12.057], [19.564, -6.686]];
pathShape.outTangents = [[24.042, -5.226], [-9.696, -9.105], [-14.408, 4.925]];
pathShape.closed = true;
layer_2_group_0_path_0.property('Path').setValue(pathShape);
var layer_2_group_0_fill_1 = layer_2_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_0_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_0_fill_1.property('Opacity').setValue(100);
var layer_2_group_0_transform = layer_2_group_0.property('Transform');
layer_2_group_0_transform.property('Position').setValue([167.172, 232.867]);
layer_2_group_0_transform.property('Scale').setValue([100, 100]);
layer_2_group_0_transform.property('Rotation').setValue(0);
layer_2_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_2_group_1 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_1.name = 'Group 2';
var layer_2_group_1_path_0 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[3.023, 22.897], [30.71, -92.632], [20.454, -93.944], [11.628, -94.419]];
pathShape.inTangents = [[-4.767, 71.522], [-6.69, 14.766], [3.497, 0.356], [2.869, 0.013]];
pathShape.outTangents = [[3.866, -58.01], [-3.342, -0.518], [-3.013, -0.304], [-42.338, 95.022]];
pathShape.closed = true;
layer_2_group_1_path_0.property('Path').setValue(pathShape);
var layer_2_group_1_fill_1 = layer_2_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_1_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_1_fill_1.property('Opacity').setValue(100);
var layer_2_group_1_transform = layer_2_group_1.property('Transform');
layer_2_group_1_transform.property('Position').setValue([106.861, 95.302]);
layer_2_group_1_transform.property('Scale').setValue([100, 100]);
layer_2_group_1_transform.property('Rotation').setValue(0);
layer_2_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_2_group_2 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_2.name = 'Group 3';
var layer_2_group_2_path_0 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[10.54, -72.743], [-8.21, 29.049], [27.687, -57.245], [28.879, -62.488]];
pathShape.inTangents = [[6.552, 3.113], [-10.5, 43.694], [0, 0], [0, 0]];
pathShape.outTangents = [[-39.418, 78.83], [10.7, -44.528], [0, 0], [-5.685, -3.693]];
pathShape.closed = true;
layer_2_group_2_path_0.property('Path').setValue(pathShape);
var layer_2_group_2_fill_1 = layer_2_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_2_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_2_fill_1.property('Opacity').setValue(100);
var layer_2_group_2_transform = layer_2_group_2.property('Transform');
layer_2_group_2_transform.property('Position').setValue([174.702, 89.842]);
layer_2_group_2_transform.property('Scale').setValue([100, 100]);
layer_2_group_2_transform.property('Rotation').setValue(0);
layer_2_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_2_group_3 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_3.name = 'Group 4';
var layer_2_group_3_path_0 = layer_2_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[17.115, 77.31], [19.514, 74.829], [-7.611, -26.187], [19.593, -74.992], [17.035, -77.31], [-10.902, -27.224], [-14.397, 22.619]];
pathShape.inTangents = [[-17.028, -16.469], [0, 0], [-11.983, 38.032], [-0.182, 0.199], [0, 0], [9.198, -29.19], [-3.105, -15.861]];
pathShape.outTangents = [[0, 0], [-29.981, -28.997], [8.973, -28.477], [0, 0], [-0.187, 0.205], [-5.423, 17.213], [3.88, 19.822]];
pathShape.closed = true;
layer_2_group_3_path_0.property('Path').setValue(pathShape);
var layer_2_group_3_fill_1 = layer_2_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_3_fill_1.property('Color').setValue([0.356863, 0.282353, 0.529412]);
layer_2_group_3_fill_1.property('Opacity').setValue(100);
var layer_2_group_3_transform = layer_2_group_3.property('Transform');
layer_2_group_3_transform.property('Position').setValue([212.725, 130.839]);
layer_2_group_3_transform.property('Scale').setValue([100, 100]);
layer_2_group_3_transform.property('Rotation').setValue(0);
layer_2_group_3_transform.property('Opacity').setValue(100);
// Shape Group: Group 5
var layer_2_group_4 = layer_2.property('Contents').addProperty('ADBE Vector Group');
layer_2_group_4.name = 'Group 5';
var layer_2_group_4_path_0 = layer_2_group_4.property('Contents').addProperty('ADBE Vector Shape - Group');
// Animated path with 9 keyframes
var pathShape_0 = new Shape();
pathShape_0.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_0.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_0.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_0.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(25/frameRate, pathShape_0);
var pathShape_1 = new Shape();
pathShape_1.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_1.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_1.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_1.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(35/frameRate, pathShape_1);
var pathShape_2 = new Shape();
pathShape_2.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_2.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_2.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_2.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(45/frameRate, pathShape_2);
var pathShape_3 = new Shape();
pathShape_3.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_3.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_3.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_3.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(75/frameRate, pathShape_3);
var pathShape_4 = new Shape();
pathShape_4.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_4.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_4.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_4.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(85/frameRate, pathShape_4);
var pathShape_5 = new Shape();
pathShape_5.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_5.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_5.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_5.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(95/frameRate, pathShape_5);
var pathShape_6 = new Shape();
pathShape_6.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_6.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_6.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_6.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(125/frameRate, pathShape_6);
var pathShape_7 = new Shape();
pathShape_7.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [128.618, 43.316], [89.028, 27.758], [132.132, 37.548], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_7.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_7.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_7.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(135/frameRate, pathShape_7);
var pathShape_8 = new Shape();
pathShape_8.vertices = [[-87.012, 122.331], [-9.707, 125.534], [63.131, 99.016], [120.595, 55.564], [89.028, 27.758], [133.87, 34.46], [135.623, -4.001], [63.119, -105.376], [44.781, -115.629], [-2.891, -130.059], [-13.146, -131.371], [-21.972, -131.846]];
pathShape_8.inTangents = [[-53.198, -32.66], [-31.943, 6.944], [-23.116, 12.056], [-13.783, 16.51], [0.179, 0.332], [-9.489, 0.008], [4.588, 13.105], [57.968, 37.69], [6.552, 3.113], [18.045, 2.786], [3.496, 0.356], [2.868, 0.014]];
pathShape_8.outTangents = [[16.31, 10.01], [24.041, -5.225], [23.123, -12.061], [-21.535, -9.534], [16.637, 5.868], [5.268, -12.399], [0, 0], [-5.684, -3.693], [-13.82, -6.566], [-3.341, -0.518], [-3.013, -0.303], [-113.962, -0.632]];
pathShape_8.closed = true;
layer_2_group_4_path_0.property('Path').setValueAtTime(145/frameRate, pathShape_8);
var layer_2_group_4_fill_1 = layer_2_group_4.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_2_group_4_fill_1.property('Color').setValue([0.494118, 0.466667, 0.800000]);
layer_2_group_4_fill_1.property('Opacity').setValue(100);
var layer_2_group_4_transform = layer_2_group_4.property('Transform');
layer_2_group_4_transform.property('Position').setValue([140.46, 132.729]);
layer_2_group_4_transform.property('Scale').setValue([100, 100]);
layer_2_group_4_transform.property('Rotation').setValue(0);
layer_2_group_4_transform.property('Opacity').setValue(100);
layer_2.inPoint = 0.000000;
layer_2.outPoint = 6.250000;
layer_2.startTime = 0.000000;
layer_2.property('Transform').property('Opacity').setValue(100);
// Smart detection: 150 keyframes for layer_2.property('Transform').property('Position')
layer_2.property('Transform').property('Position').setValueAtTime(0/frameRate, [247.758, 572.691, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(1/frameRate, [251.754, 571.095, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(2/frameRate, [256.467, 569.551, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(3/frameRate, [261.702, 567.986, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(4/frameRate, [267.26, 566.354, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(5/frameRate, [272.965, 564.621, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(6/frameRate, [278.656, 562.771, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(7/frameRate, [284.186, 560.802, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(8/frameRate, [289.426, 558.728, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(9/frameRate, [294.259, 556.58, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(10/frameRate, [298.587, 554.405, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(11/frameRate, [302.327, 552.263, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(12/frameRate, [305.409, 550.235, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(13/frameRate, [308.124, 548.317, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(14/frameRate, [310.944, 546.465, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(15/frameRate, [313.935, 544.731, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(16/frameRate, [317.14, 543.153, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(17/frameRate, [320.582, 541.752, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(18/frameRate, [324.263, 540.54, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(19/frameRate, [328.166, 539.509, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(20/frameRate, [332.251, 538.642, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(21/frameRate, [336.46, 537.904, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(22/frameRate, [340.714, 537.247, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(23/frameRate, [344.913, 536.609, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(24/frameRate, [348.936, 535.913, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(25/frameRate, [352.708, 535.053, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(26/frameRate, [356.663, 533.878, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(27/frameRate, [360.941, 532.426, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(28/frameRate, [365.56, 530.769, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(29/frameRate, [370.516, 528.977, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(30/frameRate, [375.785, 527.12, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(31/frameRate, [381.323, 525.264, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(32/frameRate, [387.061, 523.477, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(33/frameRate, [392.911, 521.824, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(34/frameRate, [398.765, 520.367, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(35/frameRate, [404.492, 519.17, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(36/frameRate, [409.939, 518.293, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(37/frameRate, [414.934, 517.796, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(38/frameRate, [419.194, 517.498, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(39/frameRate, [422.678, 517.138, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(40/frameRate, [425.611, 516.743, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(41/frameRate, [428.199, 516.343, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(42/frameRate, [430.626, 515.962, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(43/frameRate, [433.06, 515.624, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(44/frameRate, [435.646, 515.348, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(45/frameRate, [438.512, 515.153, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(46/frameRate, [441.764, 515.052, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(47/frameRate, [445.488, 515.06, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(48/frameRate, [449.753, 515.185, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(49/frameRate, [454.606, 515.435, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(50/frameRate, [460.029, 515.784, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(51/frameRate, [465.625, 515.992, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(52/frameRate, [471.187, 516.007, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(53/frameRate, [476.605, 515.835, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(54/frameRate, [481.799, 515.495, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(55/frameRate, [486.718, 515.014, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(56/frameRate, [491.342, 514.431, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(57/frameRate, [495.678, 513.795, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(58/frameRate, [499.768, 513.167, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(59/frameRate, [503.678, 512.617, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(60/frameRate, [507.508, 512.224, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(61/frameRate, [511.387, 512.082, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(62/frameRate, [515.472, 512.291, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(63/frameRate, [519.736, 513.159, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(64/frameRate, [523.99, 514.79, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(65/frameRate, [528.252, 516.907, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(66/frameRate, [532.536, 519.266, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(67/frameRate, [536.852, 521.658, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(68/frameRate, [541.205, 523.91, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(69/frameRate, [545.596, 525.885, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(70/frameRate, [550.021, 527.482, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(71/frameRate, [554.472, 528.634, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(72/frameRate, [558.939, 529.312, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(73/frameRate, [563.404, 529.521, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(74/frameRate, [567.847, 529.301, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(75/frameRate, [572.255, 528.752, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(76/frameRate, [576.704, 528.172, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(77/frameRate, [581.21, 527.72, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(78/frameRate, [585.769, 527.487, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(79/frameRate, [590.374, 527.536, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(80/frameRate, [595.015, 527.902, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(81/frameRate, [599.682, 528.589, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(82/frameRate, [604.362, 529.573, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(83/frameRate, [609.039, 530.8, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(84/frameRate, [613.695, 532.189, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(85/frameRate, [618.312, 533.628, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(86/frameRate, [622.869, 534.976, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(87/frameRate, [627.341, 536.065, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(88/frameRate, [631.641, 537.002, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(89/frameRate, [635.695, 538.085, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(90/frameRate, [639.533, 539.286, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(91/frameRate, [643.188, 540.58, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(92/frameRate, [646.698, 541.939, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(93/frameRate, [650.105, 543.338, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(94/frameRate, [653.454, 544.75, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(95/frameRate, [656.796, 546.151, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(96/frameRate, [660.185, 547.516, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(97/frameRate, [663.677, 548.82, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(98/frameRate, [667.336, 550.041, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(99/frameRate, [671.228, 551.155, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(100/frameRate, [675.426, 552.137, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(101/frameRate, [679.949, 552.969, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(102/frameRate, [684.646, 553.69, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(103/frameRate, [689.371, 554.343, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(104/frameRate, [694.009, 554.964, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(105/frameRate, [698.469, 555.587, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(106/frameRate, [702.692, 556.237, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(107/frameRate, [706.643, 556.935, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(108/frameRate, [710.318, 557.699, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(109/frameRate, [713.737, 558.538, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(110/frameRate, [716.951, 559.459, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(111/frameRate, [720.038, 560.461, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(112/frameRate, [723.103, 561.54, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(113/frameRate, [726.31, 562.633, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(114/frameRate, [729.769, 563.746, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(115/frameRate, [733.499, 564.975, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(116/frameRate, [737.502, 566.392, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(117/frameRate, [741.762, 568.041, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(118/frameRate, [746.25, 569.94, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(119/frameRate, [750.919, 572.078, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(120/frameRate, [755.706, 574.417, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(121/frameRate, [760.532, 576.894, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(122/frameRate, [765.304, 579.416, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(123/frameRate, [769.91, 581.865, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(124/frameRate, [774.223, 584.093, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(125/frameRate, [778.174, 585.963, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(126/frameRate, [782.245, 587.593, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(127/frameRate, [786.552, 588.921, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(128/frameRate, [791.086, 589.859, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(129/frameRate, [795.829, 590.354, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(130/frameRate, [800.753, 590.385, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(131/frameRate, [805.818, 589.967, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(132/frameRate, [810.974, 589.145, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(133/frameRate, [816.162, 588.002, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(134/frameRate, [821.313, 586.651, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(135/frameRate, [826.347, 585.242, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(136/frameRate, [831.173, 583.956, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(137/frameRate, [835.694, 583.009, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(138/frameRate, [839.673, 582.387, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(139/frameRate, [843.053, 581.86, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(140/frameRate, [846.045, 581.43, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(141/frameRate, [848.836, 581.09, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(142/frameRate, [851.585, 580.819, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(143/frameRate, [854.424, 580.582, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(144/frameRate, [857.455, 580.335, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(145/frameRate, [860.753, 580.02, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(146/frameRate, [864.367, 579.566, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(147/frameRate, [868.317, 578.893, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(148/frameRate, [872.595, 577.908, 0]);
layer_2.property('Transform').property('Position').setValueAtTime(149/frameRate, [877.182, 576.498, 0]);
layer_2.property('Transform').property('Scale').setValue([139, 139, 100]);
// Smart detection: 3 keyframes for layer_2.property('Transform').property('Rotation')
layer_2.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_2.property('Transform').property('Rotation').setValueAtTime(75/frameRate, -12);
layer_2.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 0);
layer_2.property('Transform').property('Anchor Point').setValue([140.461, 132.728, 0]);

// Layer 11: Water Dot
var layer_11 = createShapeLayer(comp, 'Water Dot');
// Shape Group: Group 3
var layer_11_group_0 = layer_11.property('Contents').addProperty('ADBE Vector Group');
layer_11_group_0.name = 'Group 3';
var layer_11_group_0_path_0 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_11_group_0_path_0.property('Path').setValue(pathShape);
var layer_11_group_0_fill_1 = layer_11_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_11_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_11_group_0_fill_1.property('Opacity').setValue(100);
var layer_11_group_0_transform = layer_11_group_0.property('Transform');
layer_11_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_11_group_0_transform.property('Scale').setValue([100, 100]);
layer_11_group_0_transform.property('Rotation').setValue(0);
layer_11_group_0_transform.property('Opacity').setValue(100);
layer_11.inPoint = 0.000000;
layer_11.outPoint = 6.250000;
layer_11.startTime = 0.000000;
layer_11.parent = layer_2;
layer_11.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_11.property('Transform').property('Position')
layer_11.property('Transform').property('Position').setValueAtTime(0/frameRate, [266.63, 165.048, 0]);
layer_11.property('Transform').property('Position').setValueAtTime(48.0009765625/frameRate, [635.009, -84.463, 0]);
// Smart detection: 3 keyframes for layer_11.property('Transform').property('Scale')
layer_11.property('Transform').property('Scale').setValueAtTime(0/frameRate, [0, 0, 100]);
layer_11.property('Transform').property('Scale').setValueAtTime(33.333/frameRate, [136.043, 136.043, 100]);
layer_11.property('Transform').property('Scale').setValueAtTime(50/frameRate, [0, 0, 100]);
layer_11.property('Transform').property('Rotation').setValue(0.789);
layer_11.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 10: Water Dot 5
var layer_10 = createShapeLayer(comp, 'Water Dot 5');
// Shape Group: Group 3
var layer_10_group_0 = layer_10.property('Contents').addProperty('ADBE Vector Group');
layer_10_group_0.name = 'Group 3';
var layer_10_group_0_path_0 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_10_group_0_path_0.property('Path').setValue(pathShape);
var layer_10_group_0_fill_1 = layer_10_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_10_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_10_group_0_fill_1.property('Opacity').setValue(100);
var layer_10_group_0_transform = layer_10_group_0.property('Transform');
layer_10_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_10_group_0_transform.property('Scale').setValue([100, 100]);
layer_10_group_0_transform.property('Rotation').setValue(0);
layer_10_group_0_transform.property('Opacity').setValue(100);
layer_10.inPoint = 0.000000;
layer_10.outPoint = 6.250000;
layer_10.startTime = 0.000000;
layer_10.parent = layer_2;
layer_10.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_10.property('Transform').property('Position')
layer_10.property('Transform').property('Position').setValueAtTime(0/frameRate, [262.4, 193.512, 0]);
layer_10.property('Transform').property('Position').setValueAtTime(48.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_10.property('Transform').property('Scale')
layer_10.property('Transform').property('Scale').setValueAtTime(0/frameRate, [0, 0, 100]);
layer_10.property('Transform').property('Scale').setValueAtTime(33.333/frameRate, [136.043, 136.043, 100]);
layer_10.property('Transform').property('Scale').setValueAtTime(50/frameRate, [0, 0, 100]);
layer_10.property('Transform').property('Rotation').setValue(0.789);
layer_10.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 9: Water Dot 2
var layer_9 = createShapeLayer(comp, 'Water Dot 2');
// Shape Group: Group 3
var layer_9_group_0 = layer_9.property('Contents').addProperty('ADBE Vector Group');
layer_9_group_0.name = 'Group 3';
var layer_9_group_0_path_0 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_9_group_0_path_0.property('Path').setValue(pathShape);
var layer_9_group_0_fill_1 = layer_9_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_9_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_9_group_0_fill_1.property('Opacity').setValue(100);
var layer_9_group_0_transform = layer_9_group_0.property('Transform');
layer_9_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_9_group_0_transform.property('Scale').setValue([100, 100]);
layer_9_group_0_transform.property('Rotation').setValue(0);
layer_9_group_0_transform.property('Opacity').setValue(100);
layer_9.inPoint = 0.958333;
layer_9.outPoint = 7.208333;
layer_9.startTime = 0.958333;
layer_9.parent = layer_2;
layer_9.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_9.property('Transform').property('Position')
layer_9.property('Transform').property('Position').setValueAtTime(23/frameRate, [262.4, 193.512, 0]);
layer_9.property('Transform').property('Position').setValueAtTime(71.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_9.property('Transform').property('Scale')
layer_9.property('Transform').property('Scale').setValueAtTime(23/frameRate, [0, 0, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(56.333/frameRate, [136.043, 136.043, 100]);
layer_9.property('Transform').property('Scale').setValueAtTime(73/frameRate, [0, 0, 100]);
layer_9.property('Transform').property('Rotation').setValue(0.789);
layer_9.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 8: Water Dot 3
var layer_8 = createShapeLayer(comp, 'Water Dot 3');
// Shape Group: Group 3
var layer_8_group_0 = layer_8.property('Contents').addProperty('ADBE Vector Group');
layer_8_group_0.name = 'Group 3';
var layer_8_group_0_path_0 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_8_group_0_path_0.property('Path').setValue(pathShape);
var layer_8_group_0_fill_1 = layer_8_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_8_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_8_group_0_fill_1.property('Opacity').setValue(100);
var layer_8_group_0_transform = layer_8_group_0.property('Transform');
layer_8_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_8_group_0_transform.property('Scale').setValue([100, 100]);
layer_8_group_0_transform.property('Rotation').setValue(0);
layer_8_group_0_transform.property('Opacity').setValue(100);
layer_8.inPoint = 2.708333;
layer_8.outPoint = 8.958333;
layer_8.startTime = 2.708333;
layer_8.parent = layer_2;
layer_8.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_8.property('Transform').property('Position')
layer_8.property('Transform').property('Position').setValueAtTime(65/frameRate, [262.4, 193.512, 0]);
layer_8.property('Transform').property('Position').setValueAtTime(113.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_8.property('Transform').property('Scale')
layer_8.property('Transform').property('Scale').setValueAtTime(65/frameRate, [0, 0, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(98.333/frameRate, [136.043, 136.043, 100]);
layer_8.property('Transform').property('Scale').setValueAtTime(115/frameRate, [0, 0, 100]);
layer_8.property('Transform').property('Rotation').setValue(0.789);
layer_8.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 7: Water Dot 4
var layer_7 = createShapeLayer(comp, 'Water Dot 4');
// Shape Group: Group 3
var layer_7_group_0 = layer_7.property('Contents').addProperty('ADBE Vector Group');
layer_7_group_0.name = 'Group 3';
var layer_7_group_0_path_0 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[8.304, 0], [0, 8.304], [-8.304, 0], [0, -8.304]];
pathShape.inTangents = [[0, -4.586], [4.586, 0], [0, 4.586], [-4.587, 0]];
pathShape.outTangents = [[0, 4.586], [-4.587, 0], [0, -4.586], [4.586, 0]];
pathShape.closed = true;
layer_7_group_0_path_0.property('Path').setValue(pathShape);
var layer_7_group_0_fill_1 = layer_7_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_7_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_7_group_0_fill_1.property('Opacity').setValue(100);
var layer_7_group_0_transform = layer_7_group_0.property('Transform');
layer_7_group_0_transform.property('Position').setValue([28.279, 74.042]);
layer_7_group_0_transform.property('Scale').setValue([100, 100]);
layer_7_group_0_transform.property('Rotation').setValue(0);
layer_7_group_0_transform.property('Opacity').setValue(100);
layer_7.inPoint = 4.291667;
layer_7.outPoint = 10.541667;
layer_7.startTime = 4.291667;
layer_7.parent = layer_2;
layer_7.property('Transform').property('Opacity').setValue(100);
// Smart detection: 2 keyframes for layer_7.property('Transform').property('Position')
layer_7.property('Transform').property('Position').setValueAtTime(103/frameRate, [262.4, 193.512, 0]);
layer_7.property('Transform').property('Position').setValueAtTime(151.0009765625/frameRate, [630.778, -55.999, 0]);
// Smart detection: 3 keyframes for layer_7.property('Transform').property('Scale')
layer_7.property('Transform').property('Scale').setValueAtTime(103/frameRate, [0, 0, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(136.333/frameRate, [136.043, 136.043, 100]);
layer_7.property('Transform').property('Scale').setValueAtTime(153/frameRate, [0, 0, 100]);
layer_7.property('Transform').property('Rotation').setValue(0.789);
layer_7.property('Transform').property('Anchor Point').setValue([28.279, 74.042, 0]);

// Layer 6: Fether
var layer_6 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_6_group_0 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_0.name = 'Group 1';
var layer_6_group_0_path_0 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-46.09, 13.767], [-45.711, 17.519], [-22.219, 26.135], [-0.694, 28.534], [5.426, 28.275], [5.495, 28.268], [25.187, 24.17], [46.032, 12.556], [39.753, 5.939], [20.41, 0.357], [-40.296, -25.016], [-44.465, -28.534], [-45.018, -24.881], [-42.436, -22.749], [19.788, 3.402], [42.925, 12.483], [24.407, 21.164], [5.056, 25.192], [-21.653, 23.077]];
pathShape.inTangents = [[8.722, 4.939], [-0.146, -1.256], [-6.855, -1.519], [-4.981, 0], [-0.207, 0.024], [0, 0], [-9.744, 2.531], [-0.062, 2.682], [4.283, 1.988], [8.685, 1.774], [10.138, 7.987], [1.254, 1.133], [0.173, -1.229], [-0.911, -0.721], [-32.573, -6.658], [0.017, -0.721], [17.538, -4.553], [0.673, -0.107], [14.608, 3.251]];
pathShape.outTangents = [[0.11, 1.247], [8.412, 4.487], [8.899, 1.967], [3.683, 0], [0, 0], [0.1, -0.018], [18.553, -4.815], [0.058, -2.465], [-4.149, -1.93], [-31.811, -6.503], [-1.532, -1.208], [-0.196, 1.205], [0.812, 0.7], [10.407, 8.254], [22.119, 4.521], [-0.014, 0.652], [-9.292, 2.413], [-0.687, 0.069], [-7.093, -1.578]];
pathShape.closed = true;
layer_6_group_0_path_0.property('Path').setValue(pathShape);
var layer_6_group_0_fill_1 = layer_6_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_6_group_0_fill_1.property('Opacity').setValue(100);
var layer_6_group_0_transform = layer_6_group_0.property('Transform');
layer_6_group_0_transform.property('Position').setValue([47.562, 92.948]);
layer_6_group_0_transform.property('Scale').setValue([100, 100]);
layer_6_group_0_transform.property('Rotation').setValue(0);
layer_6_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_6_group_1 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_1.name = 'Group 2';
var layer_6_group_1_path_0 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-12.022, 1.615], [53.081, 26.391], [73.287, 41.265], [75.523, 39.108], [54.676, 23.727], [-11.359, -1.422], [-67.07, -31.39], [-74.066, -41.265], [-75.523, -37.631], [-69.565, -29.53], [-48.496, -12.136]];
pathShape.inTangents = [[-13.862, -3.027], [-12.688, -7.587], [-0.062, -0.066], [0, 0], [14.034, 8.391], [31.231, 6.82], [8.588, 9.996], [1.68, 2.954], [0.486, -1.249], [-2.431, -2.847], [-8.309, -4.895]];
pathShape.outTangents = [[30.82, 6.731], [13.683, 8.184], [0, 0], [-0.272, -0.284], [-12.886, -7.708], [-30.486, -6.659], [-3.041, -3.538], [-0.484, 1.173], [1.57, 2.517], [5.67, 6.645], [10.341, 6.099]];
pathShape.closed = true;
layer_6_group_1_path_0.property('Path').setValue(pathShape);
var layer_6_group_1_fill_1 = layer_6_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_6_group_1_fill_1.property('Opacity').setValue(100);
var layer_6_group_1_transform = layer_6_group_1.property('Transform');
layer_6_group_1_transform.property('Position').setValue([89.017, 64.011]);
layer_6_group_1_transform.property('Scale').setValue([100, 100]);
layer_6_group_1_transform.property('Rotation').setValue(0);
layer_6_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_6_group_2 = layer_6.property('Contents').addProperty('ADBE Vector Group');
layer_6_group_2.name = 'Group 3';
var layer_6_group_2_path_0 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-113.599, -13.31], [-113.219, -9.558], [-93.911, 71.588], [120.441, 69.047], [10.234, -58.334], [-88.829, -119.775], [-100.12, -97.28], [-101.577, -93.645], [-111.974, -55.611], [-112.526, -51.958]];
pathShape.inTangents = [[-1.222, -13.755], [-0.146, -1.256], [-6.858, -15.895], [0, 0], [104.587, 43.146], [0, 0], [5.847, -14.19], [0.487, -1.249], [2.403, -14.801], [0.173, -1.229]];
pathShape.outTangents = [[0.11, 1.247], [1.653, 14.722], [35.898, 83.187], [0, 0], [-81.025, -33.427], [0, 0], [-0.483, 1.174], [-4.001, 10.3], [-0.196, 1.205], [-1.68, 11.883]];
pathShape.closed = true;
layer_6_group_2_path_0.property('Path').setValue(pathShape);
var layer_6_group_2_fill_1 = layer_6_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_6_group_2_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_6_group_2_fill_1.property('Opacity').setValue(100);
var layer_6_group_2_transform = layer_6_group_2.property('Transform');
layer_6_group_2_transform.property('Position').setValue([115.071, 120.025]);
layer_6_group_2_transform.property('Scale').setValue([100, 100]);
layer_6_group_2_transform.property('Rotation').setValue(0);
layer_6_group_2_transform.property('Opacity').setValue(100);
layer_6.inPoint = 0.000000;
layer_6.outPoint = 6.250000;
layer_6.startTime = 0.000000;
layer_6.parent = layer_2;
layer_6.property('Transform').property('Opacity').setValue(100);
layer_6.property('Transform').property('Position').setValue([189.857, 90.774, 0]);
layer_6.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_6.property('Transform').property('Rotation')
layer_6.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_6.property('Transform').property('Rotation').setValueAtTime(1/frameRate, -0.046);
layer_6.property('Transform').property('Rotation').setValueAtTime(2/frameRate, -0.181);
layer_6.property('Transform').property('Rotation').setValueAtTime(3/frameRate, -0.399);
layer_6.property('Transform').property('Rotation').setValueAtTime(4/frameRate, -0.695);
layer_6.property('Transform').property('Rotation').setValueAtTime(5/frameRate, -1.066);
layer_6.property('Transform').property('Rotation').setValueAtTime(6/frameRate, -1.505);
layer_6.property('Transform').property('Rotation').setValueAtTime(7/frameRate, -2.008);
layer_6.property('Transform').property('Rotation').setValueAtTime(8/frameRate, -2.569);
layer_6.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -3.184);
layer_6.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -3.847);
layer_6.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -4.555);
layer_6.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -5.301);
layer_6.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -6.08);
layer_6.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -6.889);
layer_6.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -7.721);
layer_6.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -8.572);
layer_6.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -9.436);
layer_6.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -10.309);
layer_6.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -11.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -12.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -12.932);
layer_6.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -13.79);
layer_6.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -14.632);
layer_6.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -15.453);
layer_6.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -16.248);
layer_6.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -17.011);
layer_6.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -17.738);
layer_6.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -18.424);
layer_6.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -19.064);
layer_6.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -19.653);
layer_6.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -20.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -20.657);
layer_6.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -21.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -21.397);
layer_6.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -21.655);
layer_6.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -21.832);
layer_6.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -21.923);
layer_6.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -21.923);
layer_6.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -21.832);
layer_6.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -21.655);
layer_6.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -21.397);
layer_6.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -21.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -20.657);
layer_6.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -20.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -19.653);
layer_6.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -19.064);
layer_6.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -18.424);
layer_6.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -17.738);
layer_6.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -17.011);
layer_6.property('Transform').property('Rotation').setValueAtTime(50/frameRate, -16.248);
layer_6.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -15.453);
layer_6.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -14.632);
layer_6.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -13.79);
layer_6.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -12.932);
layer_6.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -12.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -11.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -10.309);
layer_6.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -9.436);
layer_6.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -8.572);
layer_6.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -7.721);
layer_6.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -6.889);
layer_6.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -6.08);
layer_6.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -5.301);
layer_6.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -4.555);
layer_6.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -3.847);
layer_6.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -3.184);
layer_6.property('Transform').property('Rotation').setValueAtTime(67/frameRate, -2.569);
layer_6.property('Transform').property('Rotation').setValueAtTime(68/frameRate, -2.008);
layer_6.property('Transform').property('Rotation').setValueAtTime(69/frameRate, -1.505);
layer_6.property('Transform').property('Rotation').setValueAtTime(70/frameRate, -1.066);
layer_6.property('Transform').property('Rotation').setValueAtTime(71/frameRate, -0.695);
layer_6.property('Transform').property('Rotation').setValueAtTime(72/frameRate, -0.399);
layer_6.property('Transform').property('Rotation').setValueAtTime(73/frameRate, -0.181);
layer_6.property('Transform').property('Rotation').setValueAtTime(74/frameRate, -0.046);
layer_6.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 0);
layer_6.property('Transform').property('Rotation').setValueAtTime(76/frameRate, -0.046);
layer_6.property('Transform').property('Rotation').setValueAtTime(77/frameRate, -0.181);
layer_6.property('Transform').property('Rotation').setValueAtTime(78/frameRate, -0.399);
layer_6.property('Transform').property('Rotation').setValueAtTime(79/frameRate, -0.695);
layer_6.property('Transform').property('Rotation').setValueAtTime(80/frameRate, -1.066);
layer_6.property('Transform').property('Rotation').setValueAtTime(81/frameRate, -1.505);
layer_6.property('Transform').property('Rotation').setValueAtTime(82/frameRate, -2.008);
layer_6.property('Transform').property('Rotation').setValueAtTime(83/frameRate, -2.569);
layer_6.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -3.184);
layer_6.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -3.847);
layer_6.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -4.555);
layer_6.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -5.301);
layer_6.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -6.08);
layer_6.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -6.889);
layer_6.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -7.721);
layer_6.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -8.572);
layer_6.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -9.436);
layer_6.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -10.309);
layer_6.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -11.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -12.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -12.932);
layer_6.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -13.79);
layer_6.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -14.632);
layer_6.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -15.453);
layer_6.property('Transform').property('Rotation').setValueAtTime(100/frameRate, -16.248);
layer_6.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -17.011);
layer_6.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -17.738);
layer_6.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -18.424);
layer_6.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -19.064);
layer_6.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -19.653);
layer_6.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -20.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -20.657);
layer_6.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -21.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -21.397);
layer_6.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -21.655);
layer_6.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -21.832);
layer_6.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -21.923);
layer_6.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -21.923);
layer_6.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -21.832);
layer_6.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -21.655);
layer_6.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -21.397);
layer_6.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -21.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -20.657);
layer_6.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -20.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -19.653);
layer_6.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -19.064);
layer_6.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -18.424);
layer_6.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -17.738);
layer_6.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -17.011);
layer_6.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -16.248);
layer_6.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -15.453);
layer_6.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -14.632);
layer_6.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -13.79);
layer_6.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -12.932);
layer_6.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -12.062);
layer_6.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -11.186);
layer_6.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -10.309);
layer_6.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -9.436);
layer_6.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -8.572);
layer_6.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -7.721);
layer_6.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -6.889);
layer_6.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -6.08);
layer_6.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -5.301);
layer_6.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -4.555);
layer_6.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -3.847);
layer_6.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -3.184);
layer_6.property('Transform').property('Rotation').setValueAtTime(142/frameRate, -2.569);
layer_6.property('Transform').property('Rotation').setValueAtTime(143/frameRate, -2.008);
layer_6.property('Transform').property('Rotation').setValueAtTime(144/frameRate, -1.505);
layer_6.property('Transform').property('Rotation').setValueAtTime(145/frameRate, -1.066);
layer_6.property('Transform').property('Rotation').setValueAtTime(146/frameRate, -0.695);
layer_6.property('Transform').property('Rotation').setValueAtTime(147/frameRate, -0.399);
layer_6.property('Transform').property('Rotation').setValueAtTime(148/frameRate, -0.181);
layer_6.property('Transform').property('Rotation').setValueAtTime(149/frameRate, -0.046);
layer_6.property('Transform').property('Anchor Point').setValue([170.071, 173.025, 0]);

// Layer 5: Fether
var layer_5 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_5_group_0 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_0.name = 'Group 1';
var layer_5_group_0_path_0 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[12.61, -22.338], [8.318, -0.199], [-14.553, 23.882], [-13.268, 26.822], [11.204, 0.947], [13.689, -26.143], [12.598, -26.822], [11.726, -25.876], [-5.865, -11.917], [-19.872, -5.477], [-19.747, -2.26], [-4.191, -9.298]];
pathShape.inTangents = [[-2.596, 2.561], [5.537, -13.958], [9.962, -6.963], [-0.46, -0.96], [-3.769, 9.502], [1.108, 0.695], [0, 0], [0, 0], [10.49, -6.324], [5.288, -1.671], [-0.059, -1.066], [-5.236, 3.162]];
pathShape.outTangents = [[0.752, 2.261], [-3.431, 8.653], [0.401, 0.997], [10.523, -7.307], [8.667, -21.843], [0, 0], [0, 0], [-0.069, 0.076], [-3.938, 2.374], [0.024, 1.077], [5.111, -1.525], [8.181, -4.942]];
pathShape.closed = true;
layer_5_group_0_path_0.property('Path').setValue(pathShape);
var layer_5_group_0_fill_1 = layer_5_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_0_fill_1.property('Opacity').setValue(100);
var layer_5_group_0_transform = layer_5_group_0.property('Transform');
layer_5_group_0_transform.property('Position').setValue([21.07, 84.96]);
layer_5_group_0_transform.property('Scale').setValue([100, 100]);
layer_5_group_0_transform.property('Rotation').setValue(0);
layer_5_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_5_group_1 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_1.name = 'Group 2';
var layer_5_group_1_path_0 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-21.953, 22.281], [-18.608, 23.606], [-16.665, 21.898], [-2.054, 3.141], [9.205, -21.87], [14.797, -22.222], [18.439, -7.466], [6.772, 27.365], [10.441, 27.265], [21.545, -7.328], [16.343, -24.918], [7.083, -24.241], [6.592, -23.934], [6.423, -23.378], [-4.764, 1.623]];
pathShape.inTangents = [[7.338, -5.727], [-1.157, -0.403], [-0.642, 0.594], [-4.487, 7.646], [-0.855, 2.682], [-2.022, -1.184], [0.445, -10.176], [5.198, -9.099], [-1.218, 0.059], [-0.552, 12.703], [3.86, 2.24], [0.203, -0.131], [0, 0], [0, 0], [7.425, -12.641]];
pathShape.outTangents = [[1.073, 0.479], [0.652, -0.545], [5.209, -4.799], [6.734, -11.476], [1.032, -0.514], [1.906, 1.115], [-0.553, 12.744], [1.225, -0.007], [5.119, -9.247], [0.408, -9.433], [-4.224, -2.447], [0, 0], [0, 0], [-0.038, 0.124], [-4.093, 6.969]];
pathShape.closed = true;
layer_5_group_1_path_0.property('Path').setValue(pathShape);
var layer_5_group_1_fill_1 = layer_5_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_1_fill_1.property('Opacity').setValue(100);
var layer_5_group_1_transform = layer_5_group_1.property('Transform');
layer_5_group_1_transform.property('Position').setValue([50.19, 109.99]);
layer_5_group_1_transform.property('Scale').setValue([100, 100]);
layer_5_group_1_transform.property('Rotation').setValue(0);
layer_5_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_5_group_2 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_2.name = 'Group 3';
var layer_5_group_2_path_0 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-10.269, 16.102], [-3.286, 13.99], [6.831, -1.582], [10.269, -16.013], [7.162, -16.102], [3.907, -2.634]];
pathShape.inTangents = [[6.324, -4.874], [-2.296, 0.769], [-1.85, 4.564], [-0.007, 0.259], [0, 0], [3.038, -7.435]];
pathShape.outTangents = [[2.357, -0.635], [5.16, -5.326], [3.22, -7.935], [0, 0], [0, 0.058], [-3.099, 7.584]];
pathShape.closed = true;
layer_5_group_2_path_0.property('Path').setValue(pathShape);
var layer_5_group_2_fill_1 = layer_5_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_2_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_5_group_2_fill_1.property('Opacity').setValue(100);
var layer_5_group_2_transform = layer_5_group_2.property('Transform');
layer_5_group_2_transform.property('Position').setValue([92.877, 117.615]);
layer_5_group_2_transform.property('Scale').setValue([100, 100]);
layer_5_group_2_transform.property('Rotation').setValue(0);
layer_5_group_2_transform.property('Opacity').setValue(100);
// Shape Group: Group 4
var layer_5_group_3 = layer_5.property('Contents').addProperty('ADBE Vector Group');
layer_5_group_3.name = 'Group 4';
var layer_5_group_3_path_0 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-79.148, 10.657], [-79.023, 13.874], [-73.829, 40.014], [-72.544, 42.955], [-52.11, 63.444], [-48.766, 64.769], [-44.51, 66.04], [-23.385, 68.528], [-19.716, 68.428], [2.261, 64.89], [9.244, 62.778], [104.097, -17.32], [-40.875, -102.577]];
pathShape.inTangents = [[-0.949, -37.344], [-0.059, -1.067], [-3.154, -7.846], [-0.46, -0.96], [-9.686, -4.308], [-1.156, -0.403], [-1.477, -0.369], [-6.955, 0.049], [-1.219, 0.059], [-7.107, 1.919], [-2.296, 0.77], [0, 0], [0, 0]];
pathShape.outTangents = [[0.024, 1.077], [0.476, 9.334], [0.401, 0.997], [4.214, 8.939], [1.073, 0.48], [1.36, 0.477], [7.1, 1.774], [1.225, -0.007], [7.511, -0.352], [2.357, -0.634], [40.33, -13.503], [0, 0], [0, 0]];
pathShape.closed = true;
layer_5_group_3_path_0.property('Path').setValue(pathShape);
var layer_5_group_3_fill_1 = layer_5_group_3.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_5_group_3_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_5_group_3_fill_1.property('Opacity').setValue(100);
var layer_5_group_3_transform = layer_5_group_3.property('Transform');
layer_5_group_3_transform.property('Position').setValue([80.347, 68.827]);
layer_5_group_3_transform.property('Scale').setValue([100, 100]);
layer_5_group_3_transform.property('Rotation').setValue(0);
layer_5_group_3_transform.property('Opacity').setValue(100);
layer_5.inPoint = 0.000000;
layer_5.outPoint = 6.250000;
layer_5.startTime = 0.000000;
layer_5.parent = layer_2;
layer_5.property('Transform').property('Opacity').setValue(100);
layer_5.property('Transform').property('Position').setValue([157.643, 191.497, 0]);
layer_5.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_5.property('Transform').property('Rotation')
layer_5.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_5.property('Transform').property('Rotation').setValueAtTime(1/frameRate, 0.047);
layer_5.property('Transform').property('Rotation').setValueAtTime(2/frameRate, 0.183);
layer_5.property('Transform').property('Rotation').setValueAtTime(3/frameRate, 0.404);
layer_5.property('Transform').property('Rotation').setValueAtTime(4/frameRate, 0.705);
layer_5.property('Transform').property('Rotation').setValueAtTime(5/frameRate, 1.08);
layer_5.property('Transform').property('Rotation').setValueAtTime(6/frameRate, 1.525);
layer_5.property('Transform').property('Rotation').setValueAtTime(7/frameRate, 2.035);
layer_5.property('Transform').property('Rotation').setValueAtTime(8/frameRate, 2.603);
layer_5.property('Transform').property('Rotation').setValueAtTime(9/frameRate, 3.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(10/frameRate, 3.899);
layer_5.property('Transform').property('Rotation').setValueAtTime(11/frameRate, 4.616);
layer_5.property('Transform').property('Rotation').setValueAtTime(12/frameRate, 5.372);
layer_5.property('Transform').property('Rotation').setValueAtTime(13/frameRate, 6.162);
layer_5.property('Transform').property('Rotation').setValueAtTime(14/frameRate, 6.981);
layer_5.property('Transform').property('Rotation').setValueAtTime(15/frameRate, 7.825);
layer_5.property('Transform').property('Rotation').setValueAtTime(16/frameRate, 8.687);
layer_5.property('Transform').property('Rotation').setValueAtTime(17/frameRate, 9.563);
layer_5.property('Transform').property('Rotation').setValueAtTime(18/frameRate, 10.448);
layer_5.property('Transform').property('Rotation').setValueAtTime(19/frameRate, 11.337);
layer_5.property('Transform').property('Rotation').setValueAtTime(20/frameRate, 12.224);
layer_5.property('Transform').property('Rotation').setValueAtTime(21/frameRate, 13.106);
layer_5.property('Transform').property('Rotation').setValueAtTime(22/frameRate, 13.975);
layer_5.property('Transform').property('Rotation').setValueAtTime(23/frameRate, 14.829);
layer_5.property('Transform').property('Rotation').setValueAtTime(24/frameRate, 15.661);
layer_5.property('Transform').property('Rotation').setValueAtTime(25/frameRate, 16.466);
layer_5.property('Transform').property('Rotation').setValueAtTime(26/frameRate, 17.24);
layer_5.property('Transform').property('Rotation').setValueAtTime(27/frameRate, 17.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(28/frameRate, 18.672);
layer_5.property('Transform').property('Rotation').setValueAtTime(29/frameRate, 19.321);
layer_5.property('Transform').property('Rotation').setValueAtTime(30/frameRate, 19.917);
layer_5.property('Transform').property('Rotation').setValueAtTime(31/frameRate, 20.457);
layer_5.property('Transform').property('Rotation').setValueAtTime(32/frameRate, 20.935);
layer_5.property('Transform').property('Rotation').setValueAtTime(33/frameRate, 21.346);
layer_5.property('Transform').property('Rotation').setValueAtTime(34/frameRate, 21.684);
layer_5.property('Transform').property('Rotation').setValueAtTime(35/frameRate, 21.946);
layer_5.property('Transform').property('Rotation').setValueAtTime(36/frameRate, 22.125);
layer_5.property('Transform').property('Rotation').setValueAtTime(37/frameRate, 22.217);
layer_5.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 22.217);
layer_5.property('Transform').property('Rotation').setValueAtTime(39/frameRate, 22.125);
layer_5.property('Transform').property('Rotation').setValueAtTime(40/frameRate, 21.946);
layer_5.property('Transform').property('Rotation').setValueAtTime(41/frameRate, 21.684);
layer_5.property('Transform').property('Rotation').setValueAtTime(42/frameRate, 21.346);
layer_5.property('Transform').property('Rotation').setValueAtTime(43/frameRate, 20.935);
layer_5.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 20.457);
layer_5.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 19.917);
layer_5.property('Transform').property('Rotation').setValueAtTime(46/frameRate, 19.321);
layer_5.property('Transform').property('Rotation').setValueAtTime(47/frameRate, 18.672);
layer_5.property('Transform').property('Rotation').setValueAtTime(48/frameRate, 17.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 17.24);
layer_5.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 16.466);
layer_5.property('Transform').property('Rotation').setValueAtTime(51/frameRate, 15.661);
layer_5.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 14.829);
layer_5.property('Transform').property('Rotation').setValueAtTime(53/frameRate, 13.975);
layer_5.property('Transform').property('Rotation').setValueAtTime(54/frameRate, 13.106);
layer_5.property('Transform').property('Rotation').setValueAtTime(55/frameRate, 12.224);
layer_5.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 11.337);
layer_5.property('Transform').property('Rotation').setValueAtTime(57/frameRate, 10.448);
layer_5.property('Transform').property('Rotation').setValueAtTime(58/frameRate, 9.563);
layer_5.property('Transform').property('Rotation').setValueAtTime(59/frameRate, 8.687);
layer_5.property('Transform').property('Rotation').setValueAtTime(60/frameRate, 7.825);
layer_5.property('Transform').property('Rotation').setValueAtTime(61/frameRate, 6.981);
layer_5.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 6.162);
layer_5.property('Transform').property('Rotation').setValueAtTime(63/frameRate, 5.372);
layer_5.property('Transform').property('Rotation').setValueAtTime(64/frameRate, 4.616);
layer_5.property('Transform').property('Rotation').setValueAtTime(65/frameRate, 3.899);
layer_5.property('Transform').property('Rotation').setValueAtTime(66/frameRate, 3.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(67/frameRate, 2.603);
layer_5.property('Transform').property('Rotation').setValueAtTime(68/frameRate, 2.035);
layer_5.property('Transform').property('Rotation').setValueAtTime(69/frameRate, 1.525);
layer_5.property('Transform').property('Rotation').setValueAtTime(70/frameRate, 1.08);
layer_5.property('Transform').property('Rotation').setValueAtTime(71/frameRate, 0.705);
layer_5.property('Transform').property('Rotation').setValueAtTime(72/frameRate, 0.404);
layer_5.property('Transform').property('Rotation').setValueAtTime(73/frameRate, 0.183);
layer_5.property('Transform').property('Rotation').setValueAtTime(74/frameRate, 0.047);
layer_5.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 0);
layer_5.property('Transform').property('Rotation').setValueAtTime(76/frameRate, 0.047);
layer_5.property('Transform').property('Rotation').setValueAtTime(77/frameRate, 0.183);
layer_5.property('Transform').property('Rotation').setValueAtTime(78/frameRate, 0.404);
layer_5.property('Transform').property('Rotation').setValueAtTime(79/frameRate, 0.705);
layer_5.property('Transform').property('Rotation').setValueAtTime(80/frameRate, 1.08);
layer_5.property('Transform').property('Rotation').setValueAtTime(81/frameRate, 1.525);
layer_5.property('Transform').property('Rotation').setValueAtTime(82/frameRate, 2.035);
layer_5.property('Transform').property('Rotation').setValueAtTime(83/frameRate, 2.603);
layer_5.property('Transform').property('Rotation').setValueAtTime(84/frameRate, 3.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(85/frameRate, 3.899);
layer_5.property('Transform').property('Rotation').setValueAtTime(86/frameRate, 4.616);
layer_5.property('Transform').property('Rotation').setValueAtTime(87/frameRate, 5.372);
layer_5.property('Transform').property('Rotation').setValueAtTime(88/frameRate, 6.162);
layer_5.property('Transform').property('Rotation').setValueAtTime(89/frameRate, 6.981);
layer_5.property('Transform').property('Rotation').setValueAtTime(90/frameRate, 7.825);
layer_5.property('Transform').property('Rotation').setValueAtTime(91/frameRate, 8.687);
layer_5.property('Transform').property('Rotation').setValueAtTime(92/frameRate, 9.563);
layer_5.property('Transform').property('Rotation').setValueAtTime(93/frameRate, 10.448);
layer_5.property('Transform').property('Rotation').setValueAtTime(94/frameRate, 11.337);
layer_5.property('Transform').property('Rotation').setValueAtTime(95/frameRate, 12.224);
layer_5.property('Transform').property('Rotation').setValueAtTime(96/frameRate, 13.106);
layer_5.property('Transform').property('Rotation').setValueAtTime(97/frameRate, 13.975);
layer_5.property('Transform').property('Rotation').setValueAtTime(98/frameRate, 14.829);
layer_5.property('Transform').property('Rotation').setValueAtTime(99/frameRate, 15.661);
layer_5.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 16.466);
layer_5.property('Transform').property('Rotation').setValueAtTime(101/frameRate, 17.24);
layer_5.property('Transform').property('Rotation').setValueAtTime(102/frameRate, 17.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(103/frameRate, 18.672);
layer_5.property('Transform').property('Rotation').setValueAtTime(104/frameRate, 19.321);
layer_5.property('Transform').property('Rotation').setValueAtTime(105/frameRate, 19.917);
layer_5.property('Transform').property('Rotation').setValueAtTime(106/frameRate, 20.457);
layer_5.property('Transform').property('Rotation').setValueAtTime(107/frameRate, 20.935);
layer_5.property('Transform').property('Rotation').setValueAtTime(108/frameRate, 21.346);
layer_5.property('Transform').property('Rotation').setValueAtTime(109/frameRate, 21.684);
layer_5.property('Transform').property('Rotation').setValueAtTime(110/frameRate, 21.946);
layer_5.property('Transform').property('Rotation').setValueAtTime(111/frameRate, 22.125);
layer_5.property('Transform').property('Rotation').setValueAtTime(112/frameRate, 22.217);
layer_5.property('Transform').property('Rotation').setValueAtTime(113/frameRate, 22.217);
layer_5.property('Transform').property('Rotation').setValueAtTime(114/frameRate, 22.125);
layer_5.property('Transform').property('Rotation').setValueAtTime(115/frameRate, 21.946);
layer_5.property('Transform').property('Rotation').setValueAtTime(116/frameRate, 21.684);
layer_5.property('Transform').property('Rotation').setValueAtTime(117/frameRate, 21.346);
layer_5.property('Transform').property('Rotation').setValueAtTime(118/frameRate, 20.935);
layer_5.property('Transform').property('Rotation').setValueAtTime(119/frameRate, 20.457);
layer_5.property('Transform').property('Rotation').setValueAtTime(120/frameRate, 19.917);
layer_5.property('Transform').property('Rotation').setValueAtTime(121/frameRate, 19.321);
layer_5.property('Transform').property('Rotation').setValueAtTime(122/frameRate, 18.672);
layer_5.property('Transform').property('Rotation').setValueAtTime(123/frameRate, 17.977);
layer_5.property('Transform').property('Rotation').setValueAtTime(124/frameRate, 17.24);
layer_5.property('Transform').property('Rotation').setValueAtTime(125/frameRate, 16.466);
layer_5.property('Transform').property('Rotation').setValueAtTime(126/frameRate, 15.661);
layer_5.property('Transform').property('Rotation').setValueAtTime(127/frameRate, 14.829);
layer_5.property('Transform').property('Rotation').setValueAtTime(128/frameRate, 13.975);
layer_5.property('Transform').property('Rotation').setValueAtTime(129/frameRate, 13.106);
layer_5.property('Transform').property('Rotation').setValueAtTime(130/frameRate, 12.224);
layer_5.property('Transform').property('Rotation').setValueAtTime(131/frameRate, 11.337);
layer_5.property('Transform').property('Rotation').setValueAtTime(132/frameRate, 10.448);
layer_5.property('Transform').property('Rotation').setValueAtTime(133/frameRate, 9.563);
layer_5.property('Transform').property('Rotation').setValueAtTime(134/frameRate, 8.687);
layer_5.property('Transform').property('Rotation').setValueAtTime(135/frameRate, 7.825);
layer_5.property('Transform').property('Rotation').setValueAtTime(136/frameRate, 6.981);
layer_5.property('Transform').property('Rotation').setValueAtTime(137/frameRate, 6.162);
layer_5.property('Transform').property('Rotation').setValueAtTime(138/frameRate, 5.372);
layer_5.property('Transform').property('Rotation').setValueAtTime(139/frameRate, 4.616);
layer_5.property('Transform').property('Rotation').setValueAtTime(140/frameRate, 3.899);
layer_5.property('Transform').property('Rotation').setValueAtTime(141/frameRate, 3.227);
layer_5.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 2.603);
layer_5.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 2.035);
layer_5.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 1.525);
layer_5.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 1.08);
layer_5.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 0.705);
layer_5.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 0.404);
layer_5.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 0.183);
layer_5.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 0.047);
layer_5.property('Transform').property('Anchor Point').setValue([144.347, 42.827, 0]);

// Layer 4: Fether
var layer_4 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_4_group_0 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_0.name = 'Group 1';
var layer_4_group_0_path_0 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-6.927, 25.035], [-5.133, 23.862], [-1.674, 20.862], [4.812, -2.589], [6.603, -25.035], [3.497, -24.926]];
pathShape.inTangents = [[11.229, -23.003], [-0.631, 0.473], [-1.181, 1.151], [-1.119, 6.699], [0.014, 0.393], [0, 0]];
pathShape.outTangents = [[0.566, -0.312], [1.126, -0.843], [3.41, -8.401], [2.115, -12.654], [0, 0], [0.01, 0.318]];
pathShape.closed = true;
layer_4_group_0_path_0.property('Path').setValue(pathShape);
var layer_4_group_0_fill_1 = layer_4_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_4_group_0_fill_1.property('Opacity').setValue(100);
var layer_4_group_0_transform = layer_4_group_0.property('Transform');
layer_4_group_0_transform.property('Position').setValue([23.98, 53.706]);
layer_4_group_0_transform.property('Scale').setValue([100, 100]);
layer_4_group_0_transform.property('Rotation').setValue(0);
layer_4_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_4_group_1 = layer_4.property('Contents').addProperty('ADBE Vector Group');
layer_4_group_1.name = 'Group 2';
var layer_4_group_1_path_0 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-16.485, 34.655], [-14.69, 33.481], [-11.232, 30.481], [26.289, -50.837], [-31.63, -58.569]];
pathShape.inTangents = [[-16.803, 9.182], [-0.631, 0.473], [-1.181, 1.151], [0, 0], [0, 0]];
pathShape.outTangents = [[0.566, -0.312], [1.126, -0.843], [19.105, -18.591], [0, 0], [0, 0]];
pathShape.closed = true;
layer_4_group_1_path_0.property('Path').setValue(pathShape);
var layer_4_group_1_fill_1 = layer_4_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_4_group_1_fill_1.property('Color').setValue([0.972549, 0.780392, 0.235294]);
layer_4_group_1_fill_1.property('Opacity').setValue(100);
var layer_4_group_1_transform = layer_4_group_1.property('Transform');
layer_4_group_1_transform.property('Position').setValue([33.538, 44.087]);
layer_4_group_1_transform.property('Scale').setValue([100, 100]);
layer_4_group_1_transform.property('Rotation').setValue(0);
layer_4_group_1_transform.property('Opacity').setValue(100);
layer_4.inPoint = 0.000000;
layer_4.outPoint = 6.250000;
layer_4.startTime = 0.000000;
layer_4.parent = layer_2;
layer_4.property('Transform').property('Opacity').setValue(100);
layer_4.property('Transform').property('Position').setValue([211.46, 189.487, 0]);
layer_4.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_4.property('Transform').property('Rotation')
layer_4.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 0);
layer_4.property('Transform').property('Rotation').setValueAtTime(1/frameRate, 0.025);
layer_4.property('Transform').property('Rotation').setValueAtTime(2/frameRate, 0.099);
layer_4.property('Transform').property('Rotation').setValueAtTime(3/frameRate, 0.219);
layer_4.property('Transform').property('Rotation').setValueAtTime(4/frameRate, 0.382);
layer_4.property('Transform').property('Rotation').setValueAtTime(5/frameRate, 0.585);
layer_4.property('Transform').property('Rotation').setValueAtTime(6/frameRate, 0.827);
layer_4.property('Transform').property('Rotation').setValueAtTime(7/frameRate, 1.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(8/frameRate, 1.411);
layer_4.property('Transform').property('Rotation').setValueAtTime(9/frameRate, 1.749);
layer_4.property('Transform').property('Rotation').setValueAtTime(10/frameRate, 2.113);
layer_4.property('Transform').property('Rotation').setValueAtTime(11/frameRate, 2.502);
layer_4.property('Transform').property('Rotation').setValueAtTime(12/frameRate, 2.911);
layer_4.property('Transform').property('Rotation').setValueAtTime(13/frameRate, 3.34);
layer_4.property('Transform').property('Rotation').setValueAtTime(14/frameRate, 3.784);
layer_4.property('Transform').property('Rotation').setValueAtTime(15/frameRate, 4.241);
layer_4.property('Transform').property('Rotation').setValueAtTime(16/frameRate, 4.708);
layer_4.property('Transform').property('Rotation').setValueAtTime(17/frameRate, 5.183);
layer_4.property('Transform').property('Rotation').setValueAtTime(18/frameRate, 5.663);
layer_4.property('Transform').property('Rotation').setValueAtTime(19/frameRate, 6.144);
layer_4.property('Transform').property('Rotation').setValueAtTime(20/frameRate, 6.625);
layer_4.property('Transform').property('Rotation').setValueAtTime(21/frameRate, 7.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(22/frameRate, 7.574);
layer_4.property('Transform').property('Rotation').setValueAtTime(23/frameRate, 8.037);
layer_4.property('Transform').property('Rotation').setValueAtTime(24/frameRate, 8.488);
layer_4.property('Transform').property('Rotation').setValueAtTime(25/frameRate, 8.924);
layer_4.property('Transform').property('Rotation').setValueAtTime(26/frameRate, 9.343);
layer_4.property('Transform').property('Rotation').setValueAtTime(27/frameRate, 9.743);
layer_4.property('Transform').property('Rotation').setValueAtTime(28/frameRate, 10.12);
layer_4.property('Transform').property('Rotation').setValueAtTime(29/frameRate, 10.471);
layer_4.property('Transform').property('Rotation').setValueAtTime(30/frameRate, 10.795);
layer_4.property('Transform').property('Rotation').setValueAtTime(31/frameRate, 11.087);
layer_4.property('Transform').property('Rotation').setValueAtTime(32/frameRate, 11.346);
layer_4.property('Transform').property('Rotation').setValueAtTime(33/frameRate, 11.569);
layer_4.property('Transform').property('Rotation').setValueAtTime(34/frameRate, 11.752);
layer_4.property('Transform').property('Rotation').setValueAtTime(35/frameRate, 11.894);
layer_4.property('Transform').property('Rotation').setValueAtTime(36/frameRate, 11.991);
layer_4.property('Transform').property('Rotation').setValueAtTime(37/frameRate, 12.041);
layer_4.property('Transform').property('Rotation').setValueAtTime(38/frameRate, 12.041);
layer_4.property('Transform').property('Rotation').setValueAtTime(39/frameRate, 11.991);
layer_4.property('Transform').property('Rotation').setValueAtTime(40/frameRate, 11.894);
layer_4.property('Transform').property('Rotation').setValueAtTime(41/frameRate, 11.752);
layer_4.property('Transform').property('Rotation').setValueAtTime(42/frameRate, 11.569);
layer_4.property('Transform').property('Rotation').setValueAtTime(43/frameRate, 11.346);
layer_4.property('Transform').property('Rotation').setValueAtTime(44/frameRate, 11.087);
layer_4.property('Transform').property('Rotation').setValueAtTime(45/frameRate, 10.795);
layer_4.property('Transform').property('Rotation').setValueAtTime(46/frameRate, 10.471);
layer_4.property('Transform').property('Rotation').setValueAtTime(47/frameRate, 10.12);
layer_4.property('Transform').property('Rotation').setValueAtTime(48/frameRate, 9.743);
layer_4.property('Transform').property('Rotation').setValueAtTime(49/frameRate, 9.343);
layer_4.property('Transform').property('Rotation').setValueAtTime(50/frameRate, 8.924);
layer_4.property('Transform').property('Rotation').setValueAtTime(51/frameRate, 8.488);
layer_4.property('Transform').property('Rotation').setValueAtTime(52/frameRate, 8.037);
layer_4.property('Transform').property('Rotation').setValueAtTime(53/frameRate, 7.574);
layer_4.property('Transform').property('Rotation').setValueAtTime(54/frameRate, 7.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(55/frameRate, 6.625);
layer_4.property('Transform').property('Rotation').setValueAtTime(56/frameRate, 6.144);
layer_4.property('Transform').property('Rotation').setValueAtTime(57/frameRate, 5.663);
layer_4.property('Transform').property('Rotation').setValueAtTime(58/frameRate, 5.183);
layer_4.property('Transform').property('Rotation').setValueAtTime(59/frameRate, 4.708);
layer_4.property('Transform').property('Rotation').setValueAtTime(60/frameRate, 4.241);
layer_4.property('Transform').property('Rotation').setValueAtTime(61/frameRate, 3.784);
layer_4.property('Transform').property('Rotation').setValueAtTime(62/frameRate, 3.34);
layer_4.property('Transform').property('Rotation').setValueAtTime(63/frameRate, 2.911);
layer_4.property('Transform').property('Rotation').setValueAtTime(64/frameRate, 2.502);
layer_4.property('Transform').property('Rotation').setValueAtTime(65/frameRate, 2.113);
layer_4.property('Transform').property('Rotation').setValueAtTime(66/frameRate, 1.749);
layer_4.property('Transform').property('Rotation').setValueAtTime(67/frameRate, 1.411);
layer_4.property('Transform').property('Rotation').setValueAtTime(68/frameRate, 1.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(69/frameRate, 0.827);
layer_4.property('Transform').property('Rotation').setValueAtTime(70/frameRate, 0.585);
layer_4.property('Transform').property('Rotation').setValueAtTime(71/frameRate, 0.382);
layer_4.property('Transform').property('Rotation').setValueAtTime(72/frameRate, 0.219);
layer_4.property('Transform').property('Rotation').setValueAtTime(73/frameRate, 0.099);
layer_4.property('Transform').property('Rotation').setValueAtTime(74/frameRate, 0.025);
layer_4.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 0);
layer_4.property('Transform').property('Rotation').setValueAtTime(76/frameRate, 0.025);
layer_4.property('Transform').property('Rotation').setValueAtTime(77/frameRate, 0.099);
layer_4.property('Transform').property('Rotation').setValueAtTime(78/frameRate, 0.219);
layer_4.property('Transform').property('Rotation').setValueAtTime(79/frameRate, 0.382);
layer_4.property('Transform').property('Rotation').setValueAtTime(80/frameRate, 0.585);
layer_4.property('Transform').property('Rotation').setValueAtTime(81/frameRate, 0.827);
layer_4.property('Transform').property('Rotation').setValueAtTime(82/frameRate, 1.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(83/frameRate, 1.411);
layer_4.property('Transform').property('Rotation').setValueAtTime(84/frameRate, 1.749);
layer_4.property('Transform').property('Rotation').setValueAtTime(85/frameRate, 2.113);
layer_4.property('Transform').property('Rotation').setValueAtTime(86/frameRate, 2.502);
layer_4.property('Transform').property('Rotation').setValueAtTime(87/frameRate, 2.911);
layer_4.property('Transform').property('Rotation').setValueAtTime(88/frameRate, 3.34);
layer_4.property('Transform').property('Rotation').setValueAtTime(89/frameRate, 3.784);
layer_4.property('Transform').property('Rotation').setValueAtTime(90/frameRate, 4.241);
layer_4.property('Transform').property('Rotation').setValueAtTime(91/frameRate, 4.708);
layer_4.property('Transform').property('Rotation').setValueAtTime(92/frameRate, 5.183);
layer_4.property('Transform').property('Rotation').setValueAtTime(93/frameRate, 5.663);
layer_4.property('Transform').property('Rotation').setValueAtTime(94/frameRate, 6.144);
layer_4.property('Transform').property('Rotation').setValueAtTime(95/frameRate, 6.625);
layer_4.property('Transform').property('Rotation').setValueAtTime(96/frameRate, 7.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(97/frameRate, 7.574);
layer_4.property('Transform').property('Rotation').setValueAtTime(98/frameRate, 8.037);
layer_4.property('Transform').property('Rotation').setValueAtTime(99/frameRate, 8.488);
layer_4.property('Transform').property('Rotation').setValueAtTime(100/frameRate, 8.924);
layer_4.property('Transform').property('Rotation').setValueAtTime(101/frameRate, 9.343);
layer_4.property('Transform').property('Rotation').setValueAtTime(102/frameRate, 9.743);
layer_4.property('Transform').property('Rotation').setValueAtTime(103/frameRate, 10.12);
layer_4.property('Transform').property('Rotation').setValueAtTime(104/frameRate, 10.471);
layer_4.property('Transform').property('Rotation').setValueAtTime(105/frameRate, 10.795);
layer_4.property('Transform').property('Rotation').setValueAtTime(106/frameRate, 11.087);
layer_4.property('Transform').property('Rotation').setValueAtTime(107/frameRate, 11.346);
layer_4.property('Transform').property('Rotation').setValueAtTime(108/frameRate, 11.569);
layer_4.property('Transform').property('Rotation').setValueAtTime(109/frameRate, 11.752);
layer_4.property('Transform').property('Rotation').setValueAtTime(110/frameRate, 11.894);
layer_4.property('Transform').property('Rotation').setValueAtTime(111/frameRate, 11.991);
layer_4.property('Transform').property('Rotation').setValueAtTime(112/frameRate, 12.041);
layer_4.property('Transform').property('Rotation').setValueAtTime(113/frameRate, 12.041);
layer_4.property('Transform').property('Rotation').setValueAtTime(114/frameRate, 11.991);
layer_4.property('Transform').property('Rotation').setValueAtTime(115/frameRate, 11.894);
layer_4.property('Transform').property('Rotation').setValueAtTime(116/frameRate, 11.752);
layer_4.property('Transform').property('Rotation').setValueAtTime(117/frameRate, 11.569);
layer_4.property('Transform').property('Rotation').setValueAtTime(118/frameRate, 11.346);
layer_4.property('Transform').property('Rotation').setValueAtTime(119/frameRate, 11.087);
layer_4.property('Transform').property('Rotation').setValueAtTime(120/frameRate, 10.795);
layer_4.property('Transform').property('Rotation').setValueAtTime(121/frameRate, 10.471);
layer_4.property('Transform').property('Rotation').setValueAtTime(122/frameRate, 10.12);
layer_4.property('Transform').property('Rotation').setValueAtTime(123/frameRate, 9.743);
layer_4.property('Transform').property('Rotation').setValueAtTime(124/frameRate, 9.343);
layer_4.property('Transform').property('Rotation').setValueAtTime(125/frameRate, 8.924);
layer_4.property('Transform').property('Rotation').setValueAtTime(126/frameRate, 8.488);
layer_4.property('Transform').property('Rotation').setValueAtTime(127/frameRate, 8.037);
layer_4.property('Transform').property('Rotation').setValueAtTime(128/frameRate, 7.574);
layer_4.property('Transform').property('Rotation').setValueAtTime(129/frameRate, 7.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(130/frameRate, 6.625);
layer_4.property('Transform').property('Rotation').setValueAtTime(131/frameRate, 6.144);
layer_4.property('Transform').property('Rotation').setValueAtTime(132/frameRate, 5.663);
layer_4.property('Transform').property('Rotation').setValueAtTime(133/frameRate, 5.183);
layer_4.property('Transform').property('Rotation').setValueAtTime(134/frameRate, 4.708);
layer_4.property('Transform').property('Rotation').setValueAtTime(135/frameRate, 4.241);
layer_4.property('Transform').property('Rotation').setValueAtTime(136/frameRate, 3.784);
layer_4.property('Transform').property('Rotation').setValueAtTime(137/frameRate, 3.34);
layer_4.property('Transform').property('Rotation').setValueAtTime(138/frameRate, 2.911);
layer_4.property('Transform').property('Rotation').setValueAtTime(139/frameRate, 2.502);
layer_4.property('Transform').property('Rotation').setValueAtTime(140/frameRate, 2.113);
layer_4.property('Transform').property('Rotation').setValueAtTime(141/frameRate, 1.749);
layer_4.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 1.411);
layer_4.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 1.103);
layer_4.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 0.827);
layer_4.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 0.585);
layer_4.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 0.382);
layer_4.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 0.219);
layer_4.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 0.099);
layer_4.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 0.025);
layer_4.property('Transform').property('Anchor Point').setValue([26.538, 0.087, 0]);

// Layer 3: Fether
var layer_3 = createShapeLayer(comp, 'Fether');
// Shape Group: Group 1
var layer_3_group_0 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_0.name = 'Group 1';
var layer_3_group_0_path_0 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-48.907, 16.654], [-18.473, 22.785], [-8.094, 23.282], [12.696, 20.786], [29.874, 13.013], [34.52, 8.933], [41.02, 7.01], [48.7, 1.34], [43.05, -2.102], [31.141, -3.203], [27.486, -3.325], [-45.511, -23.282], [-46.036, -20.096], [-32.795, -14.522], [27.403, -0.218], [30.899, -0.104], [45.277, 1.709], [33.291, 6.041], [32.764, 6.168], [32.429, 6.596], [30.202, 8.785], [11.601, 17.862], [-48.911, 13.406]];
pathShape.inTangents = [[-0.014, -1.06], [-9.441, -0.881], [-3.352, 0], [-6.307, 1.66], [-3.538, 2.551], [-0.642, 0.725], [-2.714, 0.932], [0.222, 2.074], [2.665, 0.504], [5.167, 0.217], [1.276, 0.036], [17.535, 7.891], [0.166, -1.063], [-4.857, -1.908], [-21.087, -0.541], [-1.084, -0.041], [-1.064, -0.494], [5.302, -1.288], [0, 0], [0, 0], [1.505, -1.291], [9.785, -2.522], [29.25, 8.771]];
pathShape.outTangents = [[10.804, 3.197], [3.566, 0.331], [7.521, 0], [8.077, -2.126], [2.513, -1.816], [1.087, -0.276], [5.674, -1.954], [-0.227, -2.137], [-2.744, -0.515], [-1.16, -0.048], [-25.861, -0.662], [-0.183, 1.06], [3.914, 1.757], [15.823, 6.228], [1.249, 0.031], [10.031, 0.414], [-1.516, 1.152], [0, 0], [0, 0], [-0.017, 0.024], [-2.91, 2.495], [-11.75, 3.031], [-0.011, 1.105]];
pathShape.closed = true;
layer_3_group_0_path_0.property('Path').setValue(pathShape);
var layer_3_group_0_fill_1 = layer_3_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_0_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_3_group_0_fill_1.property('Opacity').setValue(100);
var layer_3_group_0_transform = layer_3_group_0.property('Transform');
layer_3_group_0_transform.property('Position').setValue([49.171, 76.06]);
layer_3_group_0_transform.property('Scale').setValue([100, 100]);
layer_3_group_0_transform.property('Rotation').setValue(0);
layer_3_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_3_group_1 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_1.name = 'Group 2';
var layer_3_group_1_path_0 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[35.507, 17.495], [35.799, 17.265], [35.548, 16.953], [15.438, -0.979], [-34.708, -19.19], [-35.799, -16.152], [13.671, 1.573], [33.359, 19.19]];
pathShape.inTangents = [[0, 0], [0, 0], [0.141, 0.172], [11.815, 8.187], [21.874, 1.612], [0.356, -1.035], [-10.808, -7.487], [-0.069, -0.086]];
pathShape.outTangents = [[0, 0], [-0.027, -0.036], [-1.388, -1.685], [-10.991, -7.618], [-0.372, 0.987], [21.591, 1.384], [12.629, 8.754], [0, 0]];
pathShape.closed = true;
layer_3_group_1_path_0.property('Path').setValue(pathShape);
var layer_3_group_1_fill_1 = layer_3_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_1_fill_1.property('Color').setValue([0.847059, 0.662745, 0.207843]);
layer_3_group_1_fill_1.property('Opacity').setValue(100);
var layer_3_group_1_transform = layer_3_group_1.property('Transform');
layer_3_group_1_transform.property('Position').setValue([45.759, 43.167]);
layer_3_group_1_transform.property('Scale').setValue([100, 100]);
layer_3_group_1_transform.property('Rotation').setValue(0);
layer_3_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_3_group_2 = layer_3.property('Contents').addProperty('ADBE Vector Group');
layer_3_group_2.name = 'Group 3';
var layer_3_group_2_path_0 = layer_3_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-40.844, 28.089], [-36.46, 52.294], [37.406, 31.238], [37.938, 24.449], [38.266, 20.221], [38.963, 11.331], [39.205, 8.232], [40.157, -3.962], [40.199, -4.505], [40.858, -12.945], [-13.679, -60.922], [-30.057, -40.648], [-31.148, -37.61], [-37.448, -11.846], [-37.972, -8.661], [-40.847, 24.842]];
pathShape.inTangents = [[-0.014, -1.06], [-2.827, -3.193], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [24.162, -3.452], [4.28, -11.349], [0.355, -1.035], [1.575, -9.195], [0.166, -1.063], [0.086, -10.175]];
pathShape.outTangents = [[0.125, 11.833], [10.7, 12.081], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-6.603, 0.946], [-0.373, 0.987], [-2.616, 7.653], [-0.183, 1.06], [-1.812, 11.529], [-0.011, 1.105]];
pathShape.closed = true;
layer_3_group_2_path_0.property('Path').setValue(pathShape);
var layer_3_group_2_fill_1 = layer_3_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_3_group_2_fill_1.property('Color').setValue([0.992157, 0.819608, 0.247059]);
layer_3_group_2_fill_1.property('Opacity').setValue(100);
var layer_3_group_2_transform = layer_3_group_2.property('Transform');
layer_3_group_2_transform.property('Position').setValue([41.108, 64.625]);
layer_3_group_2_transform.property('Scale').setValue([100, 100]);
layer_3_group_2_transform.property('Rotation').setValue(0);
layer_3_group_2_transform.property('Opacity').setValue(100);
layer_3.inPoint = 0.000000;
layer_3.outPoint = 6.250000;
layer_3.startTime = 0.000000;
layer_3.parent = layer_2;
layer_3.property('Transform').property('Opacity').setValue(100);
layer_3.property('Transform').property('Position').setValue([32.151, 119.159, 0]);
layer_3.property('Transform').property('Scale').setValue([100, 100, 100]);
// Smart detection: 150 keyframes for layer_3.property('Transform').property('Rotation')
layer_3.property('Transform').property('Rotation').setValueAtTime(0/frameRate, 3.064);
layer_3.property('Transform').property('Rotation').setValueAtTime(1/frameRate, 3.013);
layer_3.property('Transform').property('Rotation').setValueAtTime(2/frameRate, 2.867);
layer_3.property('Transform').property('Rotation').setValueAtTime(3/frameRate, 2.629);
layer_3.property('Transform').property('Rotation').setValueAtTime(4/frameRate, 2.305);
layer_3.property('Transform').property('Rotation').setValueAtTime(5/frameRate, 1.9);
layer_3.property('Transform').property('Rotation').setValueAtTime(6/frameRate, 1.421);
layer_3.property('Transform').property('Rotation').setValueAtTime(7/frameRate, 0.873);
layer_3.property('Transform').property('Rotation').setValueAtTime(8/frameRate, 0.26);
layer_3.property('Transform').property('Rotation').setValueAtTime(9/frameRate, -0.411);
layer_3.property('Transform').property('Rotation').setValueAtTime(10/frameRate, -1.135);
layer_3.property('Transform').property('Rotation').setValueAtTime(11/frameRate, -1.907);
layer_3.property('Transform').property('Rotation').setValueAtTime(12/frameRate, -2.721);
layer_3.property('Transform').property('Rotation').setValueAtTime(13/frameRate, -3.572);
layer_3.property('Transform').property('Rotation').setValueAtTime(14/frameRate, -4.454);
layer_3.property('Transform').property('Rotation').setValueAtTime(15/frameRate, -5.362);
layer_3.property('Transform').property('Rotation').setValueAtTime(16/frameRate, -6.291);
layer_3.property('Transform').property('Rotation').setValueAtTime(17/frameRate, -7.234);
layer_3.property('Transform').property('Rotation').setValueAtTime(18/frameRate, -8.187);
layer_3.property('Transform').property('Rotation').setValueAtTime(19/frameRate, -9.144);
layer_3.property('Transform').property('Rotation').setValueAtTime(20/frameRate, -10.1);
layer_3.property('Transform').property('Rotation').setValueAtTime(21/frameRate, -11.049);
layer_3.property('Transform').property('Rotation').setValueAtTime(22/frameRate, -11.986);
layer_3.property('Transform').property('Rotation').setValueAtTime(23/frameRate, -12.904);
layer_3.property('Transform').property('Rotation').setValueAtTime(24/frameRate, -13.8);
layer_3.property('Transform').property('Rotation').setValueAtTime(25/frameRate, -14.668);
layer_3.property('Transform').property('Rotation').setValueAtTime(26/frameRate, -15.501);
layer_3.property('Transform').property('Rotation').setValueAtTime(27/frameRate, -16.294);
layer_3.property('Transform').property('Rotation').setValueAtTime(28/frameRate, -17.043);
layer_3.property('Transform').property('Rotation').setValueAtTime(29/frameRate, -17.741);
layer_3.property('Transform').property('Rotation').setValueAtTime(30/frameRate, -18.384);
layer_3.property('Transform').property('Rotation').setValueAtTime(31/frameRate, -18.965);
layer_3.property('Transform').property('Rotation').setValueAtTime(32/frameRate, -19.48);
layer_3.property('Transform').property('Rotation').setValueAtTime(33/frameRate, -19.922);
layer_3.property('Transform').property('Rotation').setValueAtTime(34/frameRate, -20.287);
layer_3.property('Transform').property('Rotation').setValueAtTime(35/frameRate, -20.568);
layer_3.property('Transform').property('Rotation').setValueAtTime(36/frameRate, -20.762);
layer_3.property('Transform').property('Rotation').setValueAtTime(37/frameRate, -20.861);
layer_3.property('Transform').property('Rotation').setValueAtTime(38/frameRate, -20.861);
layer_3.property('Transform').property('Rotation').setValueAtTime(39/frameRate, -20.762);
layer_3.property('Transform').property('Rotation').setValueAtTime(40/frameRate, -20.568);
layer_3.property('Transform').property('Rotation').setValueAtTime(41/frameRate, -20.287);
layer_3.property('Transform').property('Rotation').setValueAtTime(42/frameRate, -19.922);
layer_3.property('Transform').property('Rotation').setValueAtTime(43/frameRate, -19.48);
layer_3.property('Transform').property('Rotation').setValueAtTime(44/frameRate, -18.965);
layer_3.property('Transform').property('Rotation').setValueAtTime(45/frameRate, -18.384);
layer_3.property('Transform').property('Rotation').setValueAtTime(46/frameRate, -17.741);
layer_3.property('Transform').property('Rotation').setValueAtTime(47/frameRate, -17.043);
layer_3.property('Transform').property('Rotation').setValueAtTime(48/frameRate, -16.294);
layer_3.property('Transform').property('Rotation').setValueAtTime(49/frameRate, -15.501);
layer_3.property('Transform').property('Rotation').setValueAtTime(50/frameRate, -14.668);
layer_3.property('Transform').property('Rotation').setValueAtTime(51/frameRate, -13.8);
layer_3.property('Transform').property('Rotation').setValueAtTime(52/frameRate, -12.904);
layer_3.property('Transform').property('Rotation').setValueAtTime(53/frameRate, -11.986);
layer_3.property('Transform').property('Rotation').setValueAtTime(54/frameRate, -11.049);
layer_3.property('Transform').property('Rotation').setValueAtTime(55/frameRate, -10.1);
layer_3.property('Transform').property('Rotation').setValueAtTime(56/frameRate, -9.144);
layer_3.property('Transform').property('Rotation').setValueAtTime(57/frameRate, -8.187);
layer_3.property('Transform').property('Rotation').setValueAtTime(58/frameRate, -7.234);
layer_3.property('Transform').property('Rotation').setValueAtTime(59/frameRate, -6.291);
layer_3.property('Transform').property('Rotation').setValueAtTime(60/frameRate, -5.362);
layer_3.property('Transform').property('Rotation').setValueAtTime(61/frameRate, -4.454);
layer_3.property('Transform').property('Rotation').setValueAtTime(62/frameRate, -3.572);
layer_3.property('Transform').property('Rotation').setValueAtTime(63/frameRate, -2.721);
layer_3.property('Transform').property('Rotation').setValueAtTime(64/frameRate, -1.907);
layer_3.property('Transform').property('Rotation').setValueAtTime(65/frameRate, -1.135);
layer_3.property('Transform').property('Rotation').setValueAtTime(66/frameRate, -0.411);
layer_3.property('Transform').property('Rotation').setValueAtTime(67/frameRate, 0.26);
layer_3.property('Transform').property('Rotation').setValueAtTime(68/frameRate, 0.873);
layer_3.property('Transform').property('Rotation').setValueAtTime(69/frameRate, 1.421);
layer_3.property('Transform').property('Rotation').setValueAtTime(70/frameRate, 1.9);
layer_3.property('Transform').property('Rotation').setValueAtTime(71/frameRate, 2.305);
layer_3.property('Transform').property('Rotation').setValueAtTime(72/frameRate, 2.629);
layer_3.property('Transform').property('Rotation').setValueAtTime(73/frameRate, 2.867);
layer_3.property('Transform').property('Rotation').setValueAtTime(74/frameRate, 3.013);
layer_3.property('Transform').property('Rotation').setValueAtTime(75/frameRate, 3.064);
layer_3.property('Transform').property('Rotation').setValueAtTime(76/frameRate, 3.013);
layer_3.property('Transform').property('Rotation').setValueAtTime(77/frameRate, 2.867);
layer_3.property('Transform').property('Rotation').setValueAtTime(78/frameRate, 2.629);
layer_3.property('Transform').property('Rotation').setValueAtTime(79/frameRate, 2.305);
layer_3.property('Transform').property('Rotation').setValueAtTime(80/frameRate, 1.9);
layer_3.property('Transform').property('Rotation').setValueAtTime(81/frameRate, 1.421);
layer_3.property('Transform').property('Rotation').setValueAtTime(82/frameRate, 0.873);
layer_3.property('Transform').property('Rotation').setValueAtTime(83/frameRate, 0.26);
layer_3.property('Transform').property('Rotation').setValueAtTime(84/frameRate, -0.411);
layer_3.property('Transform').property('Rotation').setValueAtTime(85/frameRate, -1.135);
layer_3.property('Transform').property('Rotation').setValueAtTime(86/frameRate, -1.907);
layer_3.property('Transform').property('Rotation').setValueAtTime(87/frameRate, -2.721);
layer_3.property('Transform').property('Rotation').setValueAtTime(88/frameRate, -3.572);
layer_3.property('Transform').property('Rotation').setValueAtTime(89/frameRate, -4.454);
layer_3.property('Transform').property('Rotation').setValueAtTime(90/frameRate, -5.362);
layer_3.property('Transform').property('Rotation').setValueAtTime(91/frameRate, -6.291);
layer_3.property('Transform').property('Rotation').setValueAtTime(92/frameRate, -7.234);
layer_3.property('Transform').property('Rotation').setValueAtTime(93/frameRate, -8.187);
layer_3.property('Transform').property('Rotation').setValueAtTime(94/frameRate, -9.144);
layer_3.property('Transform').property('Rotation').setValueAtTime(95/frameRate, -10.1);
layer_3.property('Transform').property('Rotation').setValueAtTime(96/frameRate, -11.049);
layer_3.property('Transform').property('Rotation').setValueAtTime(97/frameRate, -11.986);
layer_3.property('Transform').property('Rotation').setValueAtTime(98/frameRate, -12.904);
layer_3.property('Transform').property('Rotation').setValueAtTime(99/frameRate, -13.8);
layer_3.property('Transform').property('Rotation').setValueAtTime(100/frameRate, -14.668);
layer_3.property('Transform').property('Rotation').setValueAtTime(101/frameRate, -15.501);
layer_3.property('Transform').property('Rotation').setValueAtTime(102/frameRate, -16.294);
layer_3.property('Transform').property('Rotation').setValueAtTime(103/frameRate, -17.043);
layer_3.property('Transform').property('Rotation').setValueAtTime(104/frameRate, -17.741);
layer_3.property('Transform').property('Rotation').setValueAtTime(105/frameRate, -18.384);
layer_3.property('Transform').property('Rotation').setValueAtTime(106/frameRate, -18.965);
layer_3.property('Transform').property('Rotation').setValueAtTime(107/frameRate, -19.48);
layer_3.property('Transform').property('Rotation').setValueAtTime(108/frameRate, -19.922);
layer_3.property('Transform').property('Rotation').setValueAtTime(109/frameRate, -20.287);
layer_3.property('Transform').property('Rotation').setValueAtTime(110/frameRate, -20.568);
layer_3.property('Transform').property('Rotation').setValueAtTime(111/frameRate, -20.762);
layer_3.property('Transform').property('Rotation').setValueAtTime(112/frameRate, -20.861);
layer_3.property('Transform').property('Rotation').setValueAtTime(113/frameRate, -20.861);
layer_3.property('Transform').property('Rotation').setValueAtTime(114/frameRate, -20.762);
layer_3.property('Transform').property('Rotation').setValueAtTime(115/frameRate, -20.568);
layer_3.property('Transform').property('Rotation').setValueAtTime(116/frameRate, -20.287);
layer_3.property('Transform').property('Rotation').setValueAtTime(117/frameRate, -19.922);
layer_3.property('Transform').property('Rotation').setValueAtTime(118/frameRate, -19.48);
layer_3.property('Transform').property('Rotation').setValueAtTime(119/frameRate, -18.965);
layer_3.property('Transform').property('Rotation').setValueAtTime(120/frameRate, -18.384);
layer_3.property('Transform').property('Rotation').setValueAtTime(121/frameRate, -17.741);
layer_3.property('Transform').property('Rotation').setValueAtTime(122/frameRate, -17.043);
layer_3.property('Transform').property('Rotation').setValueAtTime(123/frameRate, -16.294);
layer_3.property('Transform').property('Rotation').setValueAtTime(124/frameRate, -15.501);
layer_3.property('Transform').property('Rotation').setValueAtTime(125/frameRate, -14.668);
layer_3.property('Transform').property('Rotation').setValueAtTime(126/frameRate, -13.8);
layer_3.property('Transform').property('Rotation').setValueAtTime(127/frameRate, -12.904);
layer_3.property('Transform').property('Rotation').setValueAtTime(128/frameRate, -11.986);
layer_3.property('Transform').property('Rotation').setValueAtTime(129/frameRate, -11.049);
layer_3.property('Transform').property('Rotation').setValueAtTime(130/frameRate, -10.1);
layer_3.property('Transform').property('Rotation').setValueAtTime(131/frameRate, -9.144);
layer_3.property('Transform').property('Rotation').setValueAtTime(132/frameRate, -8.187);
layer_3.property('Transform').property('Rotation').setValueAtTime(133/frameRate, -7.234);
layer_3.property('Transform').property('Rotation').setValueAtTime(134/frameRate, -6.291);
layer_3.property('Transform').property('Rotation').setValueAtTime(135/frameRate, -5.362);
layer_3.property('Transform').property('Rotation').setValueAtTime(136/frameRate, -4.454);
layer_3.property('Transform').property('Rotation').setValueAtTime(137/frameRate, -3.572);
layer_3.property('Transform').property('Rotation').setValueAtTime(138/frameRate, -2.721);
layer_3.property('Transform').property('Rotation').setValueAtTime(139/frameRate, -1.907);
layer_3.property('Transform').property('Rotation').setValueAtTime(140/frameRate, -1.135);
layer_3.property('Transform').property('Rotation').setValueAtTime(141/frameRate, -0.411);
layer_3.property('Transform').property('Rotation').setValueAtTime(142/frameRate, 0.26);
layer_3.property('Transform').property('Rotation').setValueAtTime(143/frameRate, 0.873);
layer_3.property('Transform').property('Rotation').setValueAtTime(144/frameRate, 1.421);
layer_3.property('Transform').property('Rotation').setValueAtTime(145/frameRate, 1.9);
layer_3.property('Transform').property('Rotation').setValueAtTime(146/frameRate, 2.305);
layer_3.property('Transform').property('Rotation').setValueAtTime(147/frameRate, 2.629);
layer_3.property('Transform').property('Rotation').setValueAtTime(148/frameRate, 2.867);
layer_3.property('Transform').property('Rotation').setValueAtTime(149/frameRate, 3.013);
layer_3.property('Transform').property('Anchor Point').setValue([80.172, 71.624, 0]);

// Layer 1: Eyes
var layer_1 = createShapeLayer(comp, 'Eyes');
// Shape Group: Group 1
var layer_1_group_0 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_0.name = 'Group 1';
var layer_1_group_0_path_0 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[5.22, 0], [-0.001, 5.22], [-5.22, 0], [-0.001, -5.22]];
pathShape.inTangents = [[0, -2.883], [2.883, 0], [0, 2.883], [-2.883, 0]];
pathShape.outTangents = [[0, 2.883], [-2.883, 0], [0, -2.883], [2.883, 0]];
pathShape.closed = true;
layer_1_group_0_path_0.property('Path').setValue(pathShape);
var layer_1_group_0_fill_1 = layer_1_group_0.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_0_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_1_group_0_fill_1.property('Opacity').setValue(100);
var layer_1_group_0_transform = layer_1_group_0.property('Transform');
layer_1_group_0_transform.property('Position').setValue([29.281, 19.317]);
layer_1_group_0_transform.property('Scale').setValue([100, 100]);
layer_1_group_0_transform.property('Rotation').setValue(0);
layer_1_group_0_transform.property('Opacity').setValue(100);
// Shape Group: Group 2
var layer_1_group_1 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_1.name = 'Group 2';
var layer_1_group_1_path_0 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-13.825, 0], [0.001, 13.825], [13.825, -0.001], [0.001, -13.825]];
pathShape.inTangents = [[0, -7.636], [-7.636, 0], [0, 7.636], [7.635, 0]];
pathShape.outTangents = [[0, 7.635], [7.635, 0], [0, -7.635], [-7.636, 0]];
pathShape.closed = true;
layer_1_group_1_path_0.property('Path').setValue(pathShape);
var layer_1_group_1_fill_1 = layer_1_group_1.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_1_fill_1.property('Color').setValue([0.160784, 0.164706, 0.235294]);
layer_1_group_1_fill_1.property('Opacity').setValue(100);
var layer_1_group_1_transform = layer_1_group_1.property('Transform');
layer_1_group_1_transform.property('Position').setValue([27.899, 25.667]);
layer_1_group_1_transform.property('Scale').setValue([100, 100]);
layer_1_group_1_transform.property('Rotation').setValue(0);
layer_1_group_1_transform.property('Opacity').setValue(100);
// Shape Group: Group 3
var layer_1_group_2 = layer_1.property('Contents').addProperty('ADBE Vector Group');
layer_1_group_2.name = 'Group 3';
var layer_1_group_2_path_0 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Shape - Group');
var pathShape = new Shape();
pathShape.vertices = [[-22.623, 0], [-0.001, 22.623], [22.623, 0], [0, -22.623]];
pathShape.inTangents = [[0, -12.495], [-12.494, 0], [0, 12.494], [12.494, 0]];
pathShape.outTangents = [[0, 12.494], [12.495, 0], [0, -12.495], [-12.495, 0]];
pathShape.closed = true;
layer_1_group_2_path_0.property('Path').setValue(pathShape);
var layer_1_group_2_fill_1 = layer_1_group_2.property('Contents').addProperty('ADBE Vector Graphic - Fill');
layer_1_group_2_fill_1.property('Color').setValue([1.000000, 1.000000, 1.000000]);
layer_1_group_2_fill_1.property('Opacity').setValue(100);
var layer_1_group_2_transform = layer_1_group_2.property('Transform');
layer_1_group_2_transform.property('Position').setValue([22.873, 22.874]);
layer_1_group_2_transform.property('Scale').setValue([100, 100]);
layer_1_group_2_transform.property('Rotation').setValue(0);
layer_1_group_2_transform.property('Opacity').setValue(100);
layer_1.inPoint = 0.000000;
layer_1.outPoint = 6.250000;
layer_1.startTime = 0.000000;
layer_1.parent = layer_2;
layer_1.property('Transform').property('Opacity').setValue(100);
layer_1.property('Transform').property('Position').setValue([240.907, 124.944, 0]);
// Smart detection: 45 keyframes for layer_1.property('Transform').property('Scale')
layer_1.property('Transform').property('Scale').setValueAtTime(30/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(31/frameRate, [100, 94.461, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(32/frameRate, [100, 80.175, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(33/frameRate, [100, 60.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(34/frameRate, [100, 39.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(35/frameRate, [100, 19.825, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(36/frameRate, [100, 5.539, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(37/frameRate, [100, 0, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(38/frameRate, [100, 4.297, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(39/frameRate, [100, 15.625, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(40/frameRate, [100, 31.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(42/frameRate, [100, 68.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(43/frameRate, [100, 84.375, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(44/frameRate, [100, 95.703, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(45/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(75/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(76/frameRate, [100, 94.461, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(77/frameRate, [100, 80.175, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(78/frameRate, [100, 60.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(79/frameRate, [100, 39.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(80/frameRate, [100, 19.825, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(81/frameRate, [100, 5.539, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(82/frameRate, [100, 0, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(83/frameRate, [100, 4.297, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(84/frameRate, [100, 15.625, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(85/frameRate, [100, 31.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(87/frameRate, [100, 68.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(88/frameRate, [100, 84.375, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(89/frameRate, [100, 95.703, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(90/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(120/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(121/frameRate, [100, 94.461, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(122/frameRate, [100, 80.175, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(123/frameRate, [100, 60.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(124/frameRate, [100, 39.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(125/frameRate, [100, 19.825, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(126/frameRate, [100, 5.539, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(127/frameRate, [100, 0, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(128/frameRate, [100, 4.297, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(129/frameRate, [100, 15.625, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(130/frameRate, [100, 31.641, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(132/frameRate, [100, 68.359, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(133/frameRate, [100, 84.375, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(134/frameRate, [100, 95.703, 100]);
layer_1.property('Transform').property('Scale').setValueAtTime(135/frameRate, [100, 100, 100]);
layer_1.property('Transform').property('Rotation').setValue(0);
layer_1.property('Transform').property('Anchor Point').setValue([22.873, 22.874, 0]);


// Set up track matte relationships

// Animation creation complete
alert('Animation "08" created successfully!\n' +
      'Duration: 6.00 seconds\n' +
      'Layers: 12\n' +
      'Assets: 0');

app.endUndoGroup();
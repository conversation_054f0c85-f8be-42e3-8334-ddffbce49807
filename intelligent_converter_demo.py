#!/usr/bin/env python3
"""
INTELLIGENT LOTTIE CONVERTER DEMO
Demonstrates the smart asset dependency management system
"""

import os
import json
from lottie_to_jsx_converter import LottieToJSXConverter

def create_sample_lottie_with_assets():
    """Create a sample Lottie JSON with external asset dependencies for testing"""
    sample_lottie = {
        "v": "5.7.4",
        "fr": 24,
        "ip": 0,
        "op": 150,
        "w": 1920,
        "h": 1080,
        "nm": "Sample Animation with Assets",
        "ddd": 0,
        "assets": [
            {
                "id": "image_1",
                "nm": "Background Image",
                "u": "images/",
                "p": "background.png",
                "e": 0,
                "w": 1920,
                "h": 1080
            },
            {
                "id": "image_2", 
                "nm": "Character Sprite",
                "u": "sprites/",
                "p": "character.png",
                "e": 0,
                "w": 512,
                "h": 512
            },
            {
                "id": "video_1",
                "nm": "Background Video",
                "u": "videos/",
                "p": "background_loop.mp4",
                "e": 1,
                "w": 1920,
                "h": 1080
            },
            {
                "id": "audio_1",
                "nm": "Sound Effect",
                "u": "audio/",
                "p": "sound_effect.wav",
                "t": 2
            },
            {
                "id": "embedded_img",
                "nm": "Embedded Logo",
                "p": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
                "e": 0,
                "w": 100,
                "h": 100
            }
        ],
        "fonts": {
            "list": [
                {
                    "fName": "Arial-Bold",
                    "fFamily": "Arial",
                    "fStyle": "Bold",
                    "fPath": "fonts/Arial-Bold.ttf"
                },
                {
                    "fName": "CustomFont",
                    "fFamily": "Custom Font",
                    "fStyle": "Regular",
                    "fPath": ""  # Missing font path
                }
            ]
        },
        "layers": [
            {
                "ddd": 0,
                "ind": 1,
                "ty": 2,  # Image layer
                "nm": "Background",
                "refId": "image_1",
                "sr": 1,
                "ks": {
                    "o": {"a": 0, "k": 100},
                    "r": {"a": 0, "k": 0},
                    "p": {"a": 0, "k": [960, 540, 0]},
                    "a": {"a": 0, "k": [960, 540, 0]},
                    "s": {"a": 0, "k": [100, 100, 100]}
                },
                "ao": 0,
                "ip": 0,
                "op": 150,
                "st": 0,
                "bm": 0
            },
            {
                "ddd": 0,
                "ind": 2,
                "ty": 2,  # Image layer
                "nm": "Character",
                "refId": "image_2",
                "sr": 1,
                "ks": {
                    "o": {"a": 0, "k": 100},
                    "r": {"a": 0, "k": 0},
                    "p": {"a": 0, "k": [960, 540, 0]},
                    "a": {"a": 0, "k": [256, 256, 0]},
                    "s": {"a": 0, "k": [100, 100, 100]}
                },
                "ao": 0,
                "ip": 0,
                "op": 150,
                "st": 0,
                "bm": 0
            },
            {
                "ddd": 0,
                "ind": 3,
                "ty": 2,  # Video layer
                "nm": "Background Video",
                "refId": "video_1",
                "sr": 1,
                "ks": {
                    "o": {"a": 0, "k": 50},
                    "r": {"a": 0, "k": 0},
                    "p": {"a": 0, "k": [960, 540, 0]},
                    "a": {"a": 0, "k": [960, 540, 0]},
                    "s": {"a": 0, "k": [100, 100, 100]}
                },
                "ao": 0,
                "ip": 0,
                "op": 150,
                "st": 0,
                "bm": 0
            }
        ]
    }
    
    # Save sample file
    with open('sample_with_assets.json', 'w') as f:
        json.dump(sample_lottie, f, indent=2)
    
    print("✅ Created sample_with_assets.json for testing")
    return 'sample_with_assets.json'

def demo_intelligent_conversion():
    """Demonstrate the intelligent conversion process"""
    print("🎬 INTELLIGENT LOTTIE CONVERTER DEMO")
    print("=" * 60)
    
    # Create sample file with missing assets
    sample_file = create_sample_lottie_with_assets()
    
    print(f"\n📁 Sample file created: {sample_file}")
    print("This file contains:")
    print("  • 2 missing image assets")
    print("  • 1 missing video asset") 
    print("  • 1 missing audio asset")
    print("  • 1 embedded image (no file needed)")
    print("  • 1 missing font")
    print("  • 1 available font")
    
    print("\n🚀 Starting intelligent conversion...")
    print("The converter will:")
    print("  1. Analyze all asset dependencies")
    print("  2. Open interactive file selector for missing assets")
    print("  3. Generate JSX with proper asset integration")
    print("  4. Provide detailed asset report")
    
    # Create converter and run
    converter = LottieToJSXConverter()
    
    try:
        jsx_script = converter.convert_json_to_jsx(
            sample_file, 
            'sample_with_assets_INTELLIGENT.jsx'
        )
        
        print(f"\n✅ Conversion completed!")
        print(f"📊 Generated JSX: {jsx_script.count(chr(10)) + 1:,} lines")
        print(f"💾 Output file: sample_with_assets_INTELLIGENT.jsx")
        
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        import traceback
        traceback.print_exc()

def demo_asset_analysis_only():
    """Demo just the asset analysis without full conversion"""
    print("\n🔍 ASSET ANALYSIS DEMO")
    print("=" * 40)
    
    from asset_dependency_manager import AssetDependencyManager
    
    # Check if we have any JSON files to analyze
    json_files = [f for f in os.listdir('.') if f.endswith('.json')]
    
    if not json_files:
        print("❌ No JSON files found in current directory")
        return
    
    print("📁 Available JSON files:")
    for i, file in enumerate(json_files, 1):
        print(f"  {i}. {file}")
    
    try:
        choice = input(f"\nSelect file (1-{len(json_files)}) or press Enter for first: ").strip()
        if not choice:
            choice = "1"
        
        selected_file = json_files[int(choice) - 1]
        print(f"\n🔍 Analyzing: {selected_file}")
        
        manager = AssetDependencyManager()
        dependencies = manager.analyze_dependencies(selected_file)
        
        print("\n" + manager.generate_asset_report(dependencies))
        
        if dependencies['missing_count'] > 0:
            print(f"\n⚠️  This file has {dependencies['missing_count']} missing assets")
            print("💡 Run the full conversion to resolve them interactively!")
        
    except (ValueError, IndexError):
        print("❌ Invalid selection")
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")

def main():
    """Main demo function"""
    print("🎯 INTELLIGENT ASSET DEPENDENCY DEMO")
    print("=" * 50)
    print("Choose demo mode:")
    print("1. Full intelligent conversion (with GUI asset resolver)")
    print("2. Asset analysis only (no GUI)")
    print("3. Create sample file and exit")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        demo_intelligent_conversion()
    elif choice == "2":
        demo_asset_analysis_only()
    elif choice == "3":
        create_sample_lottie_with_assets()
        print("✅ Sample file created. Run option 1 or 2 to test it!")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

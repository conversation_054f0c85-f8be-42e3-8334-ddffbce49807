#!/usr/bin/env python3
"""
INTELLIGENT LOTTIE TO JSX CONVERTER with ASSET DEPENDENCY MANAGEMENT
Converts Bodymovin/Lottie JSON files to complete After Effects JSX scripts
with smart layer ordering and interactive asset resolution
"""

import json
import math
import os
import sys
from typing import Dict, List, Any, Optional, Tuple
from asset_dependency_manager import AssetDependencyManager

class LottieToJSXConverter:
    def __init__(self):
        self.jsx_code = []
        self.layer_references = {}
        self.comp_references = {}
        self.asset_references = {}
        self.font_references = {}
        self.asset_manager = AssetDependencyManager()
        self.dependencies = None
        
    def convert_json_to_jsx(self, json_file_path: str, output_jsx_path: str):
        """Main conversion function with smart runtime asset dialogs"""
        print(f"🎬 Loading JSON file: {json_file_path}")

        with open(json_file_path, 'r', encoding='utf-8') as f:
            lottie_data = json.load(f)

        print("� Converting to JSX with smart asset dialogs...")
        jsx_script = self.generate_jsx_script(lottie_data)

        print(f"💾 Writing JSX file: {output_jsx_path}")
        with open(output_jsx_path, 'w', encoding='utf-8') as f:
            f.write(jsx_script)

        print("✅ Conversion complete!")
        print("💡 When you run this JSX in After Effects:")
        print("   - Missing assets will prompt file dialogs automatically")
        print("   - You can browse and select the required files")
        print("   - Placeholders will be created if files are not found")

        return jsx_script
    
    def generate_jsx_script(self, data: Dict) -> str:
        """Generate complete JSX script from Lottie data"""
        self.jsx_code = []
        
        # Header
        self.add_header(data)
        
        # Helper functions
        self.add_helper_functions()
        
        # Create main composition
        self.create_main_composition(data)
        
        # Process assets first
        if 'assets' in data:
            self.process_assets(data['assets'])
        
        # Process fonts
        if 'fonts' in data:
            self.process_fonts(data['fonts'])
        
        # Process all layers
        if 'layers' in data:
            self.process_layers(data['layers'], 'comp')

        # Set up track matte relationships after all layers are created
        self.setup_track_mattes(data.get('layers', []))

        # Footer
        self.add_footer(data)
        
        return '\n'.join(self.jsx_code)
    
    def add_header(self, data: Dict):
        """Add JSX script header"""
        name = data.get('nm', 'Converted Animation')
        version = data.get('v', '5.12.1')
        
        self.jsx_code.extend([
            f"// {name} - Converted from Lottie JSON",
            f"// Original Bodymovin version: {version}",
            f"// Generated by Lottie to JSX Converter",
            "",
            "// Disable undo for performance",
            "app.beginUndoGroup('Create Animation');",
            "",
        ])
    
    def add_helper_functions(self):
        """Add utility functions"""
        self.jsx_code.extend([
            "// Helper Functions",
            "function setKeyframes(property, keyframes, frameRate) {",
            "    if (!keyframes || !keyframes.length) return;",
            "    ",
            "    for (var i = 0; i < keyframes.length; i++) {",
            "        var kf = keyframes[i];",
            "        var time = kf.t / frameRate;",
            "        ",
            "        if (i === 0) {",
            "            property.setValueAtTime(time, kf.s);",
            "        } else {",
            "            property.setValueAtTime(time, kf.s);",
            "            ",
            "            // Set easing if available",
            "            if (kf.i && kf.o) {",
            "                var keyIndex = property.nearestKeyIndex(time);",
            "                try {",
            "                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);",
            "                } catch(e) {",
            "                    // Ignore easing errors",
            "                }",
            "            }",
            "        }",
            "    }",
            "}",
            "",
            "function createSolidLayer(comp, name, color, width, height) {",
            "    return comp.layers.addSolid(color, name, width, height, 1.0);",
            "}",
            "",
            "function createShapeLayer(comp, name) {",
            "    var layer = comp.layers.addShape();",
            "    layer.name = name;",
            "    return layer;",
            "}",
            "",
            "function createNullLayer(comp, name) {",
            "    var layer = comp.layers.addNull();",
            "    layer.name = name;",
            "    return layer;",
            "}",
            "",
            "function createTextLayer(comp, name) {",
            "    var layer = comp.layers.addText(name);",
            "    return layer;",
            "}",
            "",
            "function createPrecomp(comp, name, refId) {",
            "    // This would reference a precomp - simplified for now",
            "    return createNullLayer(comp, name + ' (Precomp)');",
            "}",
            "",
            "function rgbToAE(r, g, b, a) {",
            "    a = a || 1;",
            "    return [r, g, b, a];",
            "}",
            "",
            "function bezierToAE(bezierData) {",
            "    var shape = new Shape();",
            "    if (bezierData.v && bezierData.v.length > 0) {",
            "        shape.vertices = bezierData.v;",
            "        if (bezierData.i) shape.inTangents = bezierData.i;",
            "        if (bezierData.o) shape.outTangents = bezierData.o;",
            "        shape.closed = bezierData.c || false;",
            "    }",
            "    return shape;",
            "}",
            "",
            "// === INTELLIGENT ASSET IMPORT WITH CONTEXT-AWARE DIALOGS ===",
            "function importAssetWithDialog(comp, assetId, originalPath, assetType, assetName, dimensions, description) {",
            "    // Try original path first",
            "    if (originalPath && originalPath !== '') {",
            "        var originalFile = new File(originalPath);",
            "        if (originalFile.exists) {",
            "            return importAssetFile(comp, assetId, originalFile, assetName);",
            "        }",
            "    }",
            "    ",
            "    // Build hyper-intelligent dialog with detailed context",
            "    var message = '🚨 MISSING ASSET REQUIRED\\n';",
            "    message += '════════════════════════════════════════\\n\\n';",
            "    ",
            "    // Asset identification section",
            "    message += '📋 WHAT FILE IS NEEDED:\\n';",
            "    message += '• Asset Name: \"' + (assetName || assetId) + '\"\\n';",
            "    message += '• File Type: ' + assetType.toUpperCase() + '\\n';",
            "    message += '• Asset ID: ' + assetId + '\\n';",
            "    ",
            "    if (dimensions && dimensions !== '') {",
            "        message += '• Expected Size: ' + dimensions + ' pixels\\n';",
            "    }",
            "    ",
            "    message += '• Original Path: ' + (originalPath || 'Not specified') + '\\n';",
            "    ",
            "    // Context and purpose section",
            "    if (description && description !== '') {",
            "        message += '\\n🎯 PURPOSE & CONTEXT:\\n';",
            "        message += '• ' + description + '\\n';",
            "    }",
            "    ",
            "    // Smart guidance based on asset type and name",
            "    message += '\\n💡 WHAT TO LOOK FOR:\\n';",
            "    if (assetType === 'image') {",
            "        message += '• Look for PNG, JPG, or other image files\\n';",
            "        if (assetName && assetName.toLowerCase().indexOf('logo') !== -1) {",
            "            message += '• This should be your company/brand logo\\n';",
            "            message += '• Usually a PNG with transparent background\\n';",
            "        } else if (assetName && assetName.toLowerCase().indexOf('background') !== -1) {",
            "            message += '• This should be a background image\\n';",
            "            message += '• Usually covers the full composition size\\n';",
            "        } else if (assetName && assetName.toLowerCase().indexOf('placeholder') !== -1) {",
            "            message += '• This is a placeholder - replace with your image\\n';",
            "            message += '• Can be any image that fits your design\\n';",
            "        }",
            "    } else if (assetType === 'audio') {",
            "        message += '• Look for MP3, WAV, or other audio files\\n';",
            "        if (assetName && assetName.toLowerCase().indexOf('music') !== -1) {",
            "            message += '• This should be background music\\n';",
            "            message += '• Usually a longer audio track\\n';",
            "        } else {",
            "            message += '• This should be a sound effect or audio clip\\n';",
            "            message += '• Usually a shorter audio file\\n';",
            "        }",
            "    } else if (assetType === 'video') {",
            "        message += '• Look for MP4, MOV, or other video files\\n';",
            "        message += '• Should match the expected dimensions if specified\\n';",
            "    }",
            "    ",
            "    message += '\\n📂 NEXT STEPS:\\n';",
            "    message += '• Click OK to open file browser and locate the file\\n';",
            "    message += '• Click Cancel to create a placeholder instead\\n';",
            "    message += '\\n⚠️  If you can\\'t find the exact file, choose a similar one or cancel for placeholder.';",
            "    ",
            "    var userChoice = confirm(message);",
            "    if (!userChoice) {",
            "        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);",
            "    }",
            "    ",
            "    // Show file dialog with smart filters and detailed title",
            "    var fileFilter = getSmartFileFilter(assetType);",
            "    var dialogTitle = 'SELECT ' + assetType.toUpperCase() + ': ' + (assetName || assetId);",
            "    if (description) {",
            "        dialogTitle += ' (' + description + ')';",
            "    }",
            "    ",
            "    var selectedFile = File.openDialog(dialogTitle, fileFilter);",
            "    ",
            "    if (selectedFile && selectedFile.exists) {",
            "        return importAssetFile(comp, assetId, selectedFile, assetName);",
            "    } else {",
            "        alert('❌ No file selected. Creating placeholder layer instead.');",
            "        return createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions);",
            "    }",
            "}",
            "",
            "function importAssetFile(comp, assetId, file, assetName) {",
            "    try {",
            "        var importOptions = new ImportOptions(file);",
            "        var footage = app.project.importFile(importOptions);",
            "        var layer = comp.layers.add(footage);",
            "        layer.name = assetName || assetId;",
            "        return layer;",
            "    } catch (e) {",
            "        alert('Error importing file: ' + file.fsName + '\\nError: ' + e.toString() + '\\nCreating placeholder layer.');",
            "        return createNullLayer(comp, (assetName || assetId) + ' (Import Error)');",
            "    }",
            "}",
            "",
            "function createPlaceholderLayer(comp, assetId, assetName, assetType, dimensions) {",
            "    var placeholderName = (assetName || assetId) + ' [MISSING ' + assetType.toUpperCase() + ']';",
            "    ",
            "    // Create different placeholder types based on asset type",
            "    if (assetType === 'image') {",
            "        // Create a colored solid as image placeholder",
            "        var width = 500, height = 500;",
            "        if (dimensions) {",
            "            var parts = dimensions.split('x');",
            "            if (parts.length === 2) {",
            "                width = parseInt(parts[0]) || 500;",
            "                height = parseInt(parts[1]) || 500;",
            "            }",
            "        }",
            "        var placeholder = comp.layers.addSolid([0.8, 0.2, 0.2], placeholderName, width, height, 1.0);",
            "        return placeholder;",
            "    } else if (assetType === 'audio') {",
            "        // Create null layer for audio placeholder",
            "        var placeholder = comp.layers.addNull();",
            "        placeholder.name = placeholderName;",
            "        return placeholder;",
            "    } else if (assetType === 'video') {",
            "        // Create solid for video placeholder",
            "        var placeholder = comp.layers.addSolid([0.2, 0.2, 0.8], placeholderName, 1920, 1080, 1.0);",
            "        return placeholder;",
            "    } else {",
            "        // Generic null layer placeholder",
            "        var placeholder = comp.layers.addNull();",
            "        placeholder.name = placeholderName;",
            "        return placeholder;",
            "    }",
            "}",
            "",
            "function getSmartFileFilter(assetType) {",
            "    switch (assetType.toLowerCase()) {",
            "        case 'image':",
            "            return 'Image Files:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.tga;*.psd;*.ai;*.eps';",
            "        case 'video':",
            "            return 'Video Files:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv;*.m4v;*.3gp';",
            "        case 'audio':",
            "            return 'Audio Files:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a;*.wma;*.aiff';",
            "        default:",
            "            return 'All Files:*.*';",
            "    }",
            "}",
            "",
            "function getFileTypesForAsset(assetType) {",
            "    switch (assetType.toLowerCase()) {",
            "        case 'image':",
            "            return 'Images:*.png;*.jpg;*.jpeg;*.gif;*.bmp;*.tiff;*.psd';",
            "        case 'video':",
            "            return 'Videos:*.mp4;*.mov;*.avi;*.wmv;*.flv;*.webm;*.mkv';",
            "        case 'audio':",
            "            return 'Audio:*.mp3;*.wav;*.aac;*.ogg;*.flac;*.m4a';",
            "        default:",
            "            return 'All Files:*.*';",
            "    }",
            "}",
            "",
        ])
    
    def create_main_composition(self, data: Dict):
        """Create the main composition"""
        name = data.get('nm', 'Main Comp')
        width = data.get('w', 1920)
        height = data.get('h', 1080)
        duration = data.get('op', 120) / data.get('fr', 24)
        frame_rate = data.get('fr', 24)
        
        self.jsx_code.extend([
            f"// Create main composition: {name}",
            f"var comp = app.project.items.addComp('{name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
            f"var frameRate = {frame_rate};",
            "",
        ])
    
    def process_assets(self, assets: List[Dict]):
        """Process asset references and create precomps"""
        self.jsx_code.append("// Asset References and Precomps")

        for asset in assets:
            asset_id = asset.get('id', '')
            asset_type = 'unknown'

            if 'e' in asset:
                if asset['e'] == 0:
                    asset_type = 'image'
                elif asset['e'] == 1:
                    asset_type = 'video'
            elif 't' in asset and asset['t'] == 2:
                asset_type = 'audio'
            elif 'layers' in asset:
                asset_type = 'precomp'

            self.asset_references[asset_id] = asset

            if asset_type == 'precomp':
                self.create_precomp_composition(asset)
            else:
                self.jsx_code.append(f"// Asset: {asset_id} ({asset_type})")

        self.jsx_code.append("")

    def create_precomp_composition(self, asset: Dict):
        """Create a precomp composition"""
        comp_name = asset.get('nm', 'Precomp')
        comp_id = asset.get('id', '')
        width = asset.get('w', 1920)
        height = asset.get('h', 1080)
        frame_rate = asset.get('fr', 24)
        duration = 5.0  # Default duration

        if 'layers' in asset and len(asset['layers']) > 0:
            # Try to determine duration from layers
            max_out_point = 0
            for layer in asset['layers']:
                if 'op' in layer:
                    max_out_point = max(max_out_point, layer['op'])
            if max_out_point > 0:
                duration = max_out_point / frame_rate

        comp_var = f"comp_{comp_id.replace('comp_', '')}"
        self.comp_references[comp_id] = comp_var

        self.jsx_code.extend([
            f"// Create precomp: {comp_name}",
            f"var {comp_var} = app.project.items.addComp('{comp_name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
            "",
        ])

        # Process layers in this precomp
        if 'layers' in asset:
            self.jsx_code.append(f"// Processing {len(asset['layers'])} layers in {comp_name}")

            # Sort layers by dependency order (parents first, then by normal index for AE stacking)
            sorted_layers = self.sort_layers_by_dependencies(asset['layers'])

            # Store current layer references to restore later
            old_layer_references = self.layer_references.copy()

            # Clear layer references for this precomp
            precomp_layer_references = {}

            for layer in sorted_layers:
                self.process_single_layer_in_precomp(layer, comp_var, precomp_layer_references)

            # Restore original layer references
            self.layer_references = old_layer_references

        self.jsx_code.append("")
    
    def process_fonts(self, fonts: Dict):
        """Process font data"""
        if 'list' in fonts:
            self.jsx_code.append("// Font References")
            for font in fonts['list']:
                font_name = font.get('fName', 'Unknown')
                font_family = font.get('fFamily', 'Unknown')
                self.jsx_code.append(f"// Font: {font_name} ({font_family})")
                self.font_references[font_name] = font
            self.jsx_code.append("")
    
    def process_layers(self, layers: List[Dict], parent_comp: str):
        """Process all layers in a composition"""
        self.jsx_code.append(f"// Creating {len(layers)} layers")

        # Sort layers by dependency order (parents first, then by normal index for AE stacking)
        sorted_layers = self.sort_layers_by_dependencies(layers)

        for layer in sorted_layers:
            self.process_single_layer(layer, parent_comp)

        self.jsx_code.append("")

    # REMOVED: Semantic analysis was causing incorrect layer ordering
    # The original JSON order is the correct visual stacking order

    def sort_layers_by_dependencies(self, layers: List[Dict]) -> List[Dict]:
        """Sort layers respecting ORIGINAL JSON ORDER and parent-child dependencies.
        The original JSON order is the intended visual stacking order."""

        # Create a map of layer index to layer data
        layer_map = {layer.get('ind', 0): layer for layer in layers}

        # Track processed layers
        processed = set()
        result = []

        def process_layer_and_dependencies(layer_index: int):
            if layer_index in processed or layer_index not in layer_map:
                return

            layer = layer_map[layer_index]

            # Process parent first if it exists
            if 'parent' in layer:
                parent_index = layer['parent']
                if parent_index in layer_map and parent_index not in processed:
                    process_layer_and_dependencies(parent_index)

            # Process track matte parent if it exists
            if 'tp' in layer:
                matte_index = layer['tp']
                if matte_index in layer_map and matte_index not in processed:
                    process_layer_and_dependencies(matte_index)

            # Add this layer to result
            result.append(layer)
            processed.add(layer_index)

        # PRESERVE ORIGINAL JSON ORDER: This is the intended visual stacking
        # Process layers in their original JSON order to maintain proper depth
        self.jsx_code.append("// Preserving original JSON layer order for correct visual stacking")

        for layer in layers:
            layer_index = layer.get('ind', 0)
            name = layer.get('nm', '')
            self.jsx_code.append(f"// {name} (index {layer_index})")
            process_layer_and_dependencies(layer_index)

        return result
    
    def process_single_layer(self, layer: Dict, parent_comp: str):
        """Process a single layer"""
        layer_type = layer.get('ty', 0)
        layer_name = layer.get('nm', f'Layer {layer.get("ind", 0)}')
        layer_index = layer.get('ind', 0)
        
        self.jsx_code.append(f"// Layer {layer_index}: {layer_name}")
        
        # Create layer based on type
        if layer_type == 0:  # Precomp
            self.create_precomp_layer(layer, parent_comp)
        elif layer_type == 1:  # Solid
            self.create_solid_layer(layer, parent_comp)
        elif layer_type == 2:  # Image
            self.create_image_layer(layer, parent_comp)
        elif layer_type == 3:  # Null
            self.create_null_layer(layer, parent_comp)
        elif layer_type == 4:  # Shape
            self.create_shape_layer(layer, parent_comp)
        elif layer_type == 5:  # Text
            self.create_text_layer(layer, parent_comp)
        elif layer_type == 6:  # Audio
            self.create_audio_layer(layer, parent_comp)
        else:
            self.jsx_code.append(f"// Unknown layer type: {layer_type}")
            return
        
        # Store layer reference
        layer_var = f"layer_{layer_index}"
        self.layer_references[layer_index] = layer_var
        
        # Set common layer properties
        self.set_layer_properties(layer, layer_var)

        # Track matte setup is handled centrally - no additional setup needed here
        
        self.jsx_code.append("")

    def process_single_layer_in_precomp(self, layer: Dict, parent_comp_var: str, layer_references: Dict):
        """Process a single layer within a precomp composition"""
        # Temporarily store the current composition context
        old_comp_context = getattr(self, '_current_comp_var', 'comp')
        self._current_comp_var = parent_comp_var

        # Process the layer normally but with the precomp context
        self.process_single_layer(layer, parent_comp_var)

        # Store layer reference in the precomp's layer references
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        layer_references[layer_index] = layer_var

        # Restore the original composition context
        self._current_comp_var = old_comp_context

    def create_solid_layer(self, layer: Dict, parent_comp: str):
        """Create solid layer"""
        layer_name = layer.get('nm', 'Solid')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get solid color (default to white if not found)
        solid_color = layer.get('sc', '#ffffff')
        if solid_color.startswith('#'):
            # Convert hex to RGB
            r = int(solid_color[1:3], 16) / 255.0
            g = int(solid_color[3:5], 16) / 255.0
            b = int(solid_color[5:7], 16) / 255.0
            color_array = f"[{r:.6f}, {g:.6f}, {b:.6f}]"
        else:
            color_array = "[1, 1, 1]"  # Default white

        width = layer.get('sw', 1920)
        height = layer.get('sh', 1080)

        self.jsx_code.extend([
            f"var {layer_var} = createSolidLayer({comp_var}, '{layer_name}', {color_array}, {width}, {height});",
        ])
    
    def create_shape_layer(self, layer: Dict, parent_comp: str):
        """Create shape layer with all shapes and effects"""
        layer_name = layer.get('nm', 'Shape')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createShapeLayer({comp_var}, '{layer_name}');",
        ])

        # Process shapes
        if 'shapes' in layer:
            self.process_shapes(layer['shapes'], layer_var)

        # Process effects (Drop Shadow, Glow, etc.)
        if 'ef' in layer:
            self.process_layer_effects(layer['ef'], layer_var)

    def process_layer_effects(self, effects: List[Dict], layer_var: str):
        """Process all layer effects including Drop Shadow, Glow, etc."""
        for i, effect in enumerate(effects):
            effect_type = effect.get('ty', 0)
            effect_name = effect.get('nm', f'Effect {i+1}')

            self.jsx_code.append(f"// Effect: {effect_name} (type {effect_type})")

            # Map common effect types
            if effect_type == 25:  # Drop Shadow
                self.create_drop_shadow_effect(effect, layer_var, i)
            elif effect_type == 29:  # Glow
                self.create_glow_effect(effect, layer_var, i)
            elif effect_type == 20:  # Fill
                self.create_fill_effect(effect, layer_var, i)
            elif effect_type == 21:  # Stroke
                self.create_stroke_effect(effect, layer_var, i)
            else:
                # Generic effect handling
                self.create_generic_effect(effect, layer_var, i)

    def create_drop_shadow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Drop Shadow effect"""
        effect_var = f"{layer_var}_dropShadow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Drop Shadow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_glow_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Glow effect"""
        effect_var = f"{layer_var}_glow_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Glow');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_fill_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Fill effect"""
        effect_var = f"{layer_var}_fill_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Fill');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_stroke_effect(self, effect: Dict, layer_var: str, index: int):
        """Create Stroke effect"""
        effect_var = f"{layer_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {effect_var} = {layer_var}.property('Effects').addProperty('ADBE Stroke');",
        ])

        # Process effect properties
        if 'ef' in effect:
            for prop in effect['ef']:
                self.process_effect_property(prop, effect_var)

    def create_generic_effect(self, effect: Dict, layer_var: str, index: int):
        """Create generic effect with custom properties"""
        effect_name = effect.get('nm', f'Effect_{index}')
        effect_var = f"{layer_var}_effect_{index}"

        self.jsx_code.extend([
            f"// Generic effect: {effect_name}",
            f"// Note: This effect may need manual recreation in After Effects",
        ])

        # Process effect properties as comments for manual recreation
        if 'ef' in effect:
            for prop in effect['ef']:
                prop_name = prop.get('nm', 'Unknown')
                if 'v' in prop and 'k' in prop['v']:
                    value = prop['v']['k']
                    self.jsx_code.append(f"// Property '{prop_name}': {value}")

    def process_effect_property(self, prop: Dict, effect_var: str):
        """Process individual effect property"""
        prop_name = prop.get('nm', 'Unknown')
        prop_type = prop.get('ty', 0)

        if 'v' in prop and 'k' in prop['v']:
            value = prop['v']['k']

            # Map property types to After Effects property names
            ae_prop_name = self.map_effect_property_name(prop_name, prop_type)

            if isinstance(value, list) and len(value) >= 3:
                # Color property
                color_str = f"[{value[0]:.6f}, {value[1]:.6f}, {value[2]:.6f}]"
                self.jsx_code.append(f"{effect_var}.property('{ae_prop_name}').setValue({color_str});")
            elif isinstance(value, (int, float)):
                # Numeric property
                self.jsx_code.append(f"{effect_var}.property('{ae_prop_name}').setValue({value});")
            else:
                # Other property types
                self.jsx_code.append(f"// {ae_prop_name}: {value}")

    def map_effect_property_name(self, lottie_name: str, prop_type: int) -> str:
        """Map Lottie property names to After Effects property names"""
        name_mapping = {
            'Shadow Color': 'Shadow Color',
            'Opacity': 'Opacity',
            'Direction': 'Direction',
            'Distance': 'Distance',
            'Softness': 'Softness',
            'Glow Color': 'Glow Color',
            'Glow Intensity': 'Glow Intensity',
            'Glow Radius': 'Glow Radius',
            'Fill Color': 'Color',
            'Stroke Color': 'Color',
            'Stroke Width': 'Brush Size'
        }

        return name_mapping.get(lottie_name, lottie_name)

    def create_null_layer(self, layer: Dict, parent_comp: str):
        """Create null layer"""
        layer_name = layer.get('nm', 'Null')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createNullLayer({comp_var}, '{layer_name}');",
        ])

    def create_text_layer(self, layer: Dict, parent_comp: str):
        """Create text layer with proper text content"""
        layer_name = layer.get('nm', 'Text')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        self.jsx_code.extend([
            f"var {layer_var} = createTextLayer({comp_var}, '{layer_name}');",
        ])

        # Set text properties from Lottie data
        if 't' in layer and 'd' in layer['t'] and 'k' in layer['t']['d']:
            text_data = layer['t']['d']['k']
            if text_data and len(text_data) > 0:
                text_info = text_data[0].get('s', {})
                text_content = text_info.get('t', '').replace("'", "\\'")  # Escape quotes
                font_size = text_info.get('s', 50)
                font_name = text_info.get('f', 'Arial')

                # Handle text color
                if 'fc' in text_info:
                    color = text_info['fc']
                    if len(color) >= 3:
                        color_str = f"[{color[0]:.6f}, {color[1]:.6f}, {color[2]:.6f}]"
                    else:
                        color_str = "[1, 1, 1]"
                else:
                    color_str = "[1, 1, 1]"

                # Handle text justification
                justification = text_info.get('j', 0)  # 0=left, 1=right, 2=center
                justification_map = {
                    0: "ParagraphJustification.LEFT_JUSTIFY",
                    1: "ParagraphJustification.RIGHT_JUSTIFY",
                    2: "ParagraphJustification.CENTER_JUSTIFY"
                }
                justify_str = justification_map.get(justification, "ParagraphJustification.LEFT_JUSTIFY")

                self.jsx_code.extend([
                    f"var textDoc = {layer_var}.property('Source Text');",
                    f"var textValue = textDoc.value;",
                    f"textValue.text = '{text_content}';",
                    f"textValue.font = '{font_name}';",
                    f"textValue.fontSize = {font_size};",
                    f"textValue.fillColor = {color_str};",
                    f"textValue.justification = {justify_str};",
                    f"textDoc.setValue(textValue);",
                ])
            else:
                self.jsx_code.append(f"// No text data found for {layer_name}")
        else:
            self.jsx_code.append(f"// No text properties found for {layer_name}")
    
    def create_precomp_layer(self, layer: Dict, parent_comp: str):
        """Create precomp layer with actual precomp reference"""
        layer_name = layer.get('nm', 'Precomp')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        if ref_id in self.comp_references:
            # Reference the actual precomp
            precomp_var = self.comp_references[ref_id]
            self.jsx_code.extend([
                f"var {layer_var} = {comp_var}.layers.add({precomp_var});",
                f"{layer_var}.name = '{layer_name}';",
            ])
        else:
            # Fallback to null layer if precomp not found
            self.jsx_code.extend([
                f"var {layer_var} = createNullLayer({comp_var}, '{layer_name} (Missing Precomp: {ref_id})');",
                f"// Warning: Precomp {ref_id} not found",
            ])

    def create_image_layer(self, layer: Dict, parent_comp: str):
        """Create image layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Image')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        dimensions = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Get dimensions if available
            width = asset_info.get('w', 0)
            height = asset_info.get('h', 0)
            if width and height:
                dimensions = f"{width}x{height}"

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Image Layer: {layer_name} (Asset: {ref_id})",
            f"var {layer_var} = importAssetWithDialog({comp_var}, '{ref_id}', '{original_path}', 'image', '{layer_name}', '{dimensions}', '{description}');",
        ])

    def get_asset_description_from_metadata(self, asset_info: Dict, layer_name: str) -> str:
        """Generate intelligent description based on asset metadata and context"""
        asset_name = asset_info.get('nm', layer_name).lower()
        filename = asset_info.get('p', '').lower()

        # Analyze name patterns for smart descriptions
        if any(keyword in asset_name for keyword in ['logo', 'brand', 'company']):
            return 'Company or brand logo image'
        elif any(keyword in asset_name for keyword in ['background', 'bg', 'backdrop']):
            return 'Background image for the scene'
        elif any(keyword in asset_name for keyword in ['placeholder', 'temp', 'dummy']):
            return 'Placeholder image to be replaced with final content'
        elif any(keyword in asset_name for keyword in ['character', 'person', 'avatar']):
            return 'Character or person image'
        elif any(keyword in asset_name for keyword in ['icon', 'symbol', 'graphic']):
            return 'Icon or graphic element'
        elif any(keyword in asset_name for keyword in ['texture', 'pattern', 'material']):
            return 'Texture or pattern overlay'
        elif any(keyword in filename for keyword in ['img', 'image', 'pic', 'photo']):
            return 'General image asset for visual content'
        elif any(keyword in filename for keyword in ['music', 'song', 'track']):
            return 'Background music or soundtrack'
        elif any(keyword in filename for keyword in ['sound', 'sfx', 'effect', 'audio']):
            return 'Sound effect or audio clip'
        elif any(keyword in filename for keyword in ['voice', 'speech', 'narration']):
            return 'Voice-over or narration audio'
        else:
            return 'Media asset for the animation'

    def create_audio_layer(self, layer: Dict, parent_comp: str):
        """Create audio layer with smart file dialog prompts"""
        layer_name = layer.get('nm', 'Audio')
        layer_index = layer.get('ind', 0)
        layer_var = f"layer_{layer_index}"
        ref_id = layer.get('refId', '')

        # Get the current composition context
        comp_var = getattr(self, '_current_comp_var', 'comp')

        # Get asset information with enhanced metadata
        asset_info = self.asset_references.get(ref_id, {})
        original_path = ''
        description = ''

        if asset_info:
            # Build original path from asset data
            folder = asset_info.get('u', '')
            filename = asset_info.get('p', '')
            if folder and filename:
                original_path = folder + filename

            # Generate smart description
            description = self.get_asset_description_from_metadata(asset_info, layer_name)

        self.jsx_code.extend([
            f"// Audio Layer: {layer_name} (Asset: {ref_id})",
            f"var {layer_var} = importAssetWithDialog({comp_var}, '{ref_id}', '{original_path}', 'audio', '{layer_name}', '', '{description}');",
        ])

    def process_shapes(self, shapes: List[Dict], layer_var: str):
        """Process all shapes in a shape layer"""
        for i, shape in enumerate(shapes):
            shape_type = shape.get('ty', '')

            if shape_type == 'gr':  # Group
                self.process_shape_group(shape, layer_var, i)
            elif shape_type == 'el':  # Ellipse
                self.process_ellipse_direct(shape, layer_var, i)
            elif shape_type == 'rc':  # Rectangle
                self.process_rectangle_direct(shape, layer_var, i)
            elif shape_type == 'sh':  # Path
                self.process_path_direct(shape, layer_var, i)
            elif shape_type == 'fl':  # Fill
                self.process_fill_direct(shape, layer_var, i)
            elif shape_type == 'st':  # Stroke
                self.process_stroke_direct(shape, layer_var, i)
            elif shape_type == 'tm':  # Trim
                self.process_trim_direct(shape, layer_var, i)
            elif shape_type == 'tr':  # Transform
                self.process_shape_transform_direct(shape, layer_var, i)

    def process_ellipse_direct(self, shape: Dict, layer_var: str, index: int):
        """Process ellipse shape directly in layer"""
        ellipse_var = f"{layer_var}_ellipse_{index}"
        self.jsx_code.extend([
            f"var {ellipse_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_rectangle_direct(self, shape: Dict, layer_var: str, index: int):
        """Process rectangle shape directly in layer"""
        rect_var = f"{layer_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in shape and 'k' in shape['s']:
            size_data = shape['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

    def process_path_direct(self, shape: Dict, layer_var: str, index: int):
        """Process path shape directly in layer"""
        path_var = f"{layer_var}_path_{index}"
        self.jsx_code.extend([
            f"var {path_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        if 'ks' in shape and 'k' in shape['ks']:
            path_data = shape['ks']['k']
            if isinstance(path_data, dict):
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                self.set_animated_path(path_var, path_data)

    def process_fill_direct(self, shape: Dict, layer_var: str, index: int):
        """Process fill directly in layer"""
        fill_var = f"{layer_var}_fill_{index}"
        self.jsx_code.extend([
            f"var {fill_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

    def process_stroke_direct(self, shape: Dict, layer_var: str, index: int):
        """Process stroke directly in layer"""
        stroke_var = f"{layer_var}_stroke_{index}"
        self.jsx_code.extend([
            f"var {stroke_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        if 'c' in shape and 'k' in shape['c']:
            color_data = shape['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        if 'w' in shape and 'k' in shape['w']:
            width_data = shape['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

    def process_trim_direct(self, shape: Dict, layer_var: str, index: int):
        """Process trim paths directly in layer"""
        trim_var = f"{layer_var}_trim_{index}"
        self.jsx_code.extend([
            f"var {trim_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        if 's' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Start')", shape['s'])
        if 'e' in shape:
            self.set_property_keyframes(f"{trim_var}.property('End')", shape['e'])
        if 'o' in shape:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", shape['o'])

    def process_shape_transform_direct(self, shape: Dict, layer_var: str, index: int):
        """Process shape transform directly in layer"""
        # Shape transforms are handled differently - this is a placeholder
        self.jsx_code.append(f"// Shape transform {index} - TODO: implement")

    def process_rectangle_in_group(self, item: Dict, parent_var: str, index: int):
        """Process rectangle shape in group"""
        rect_var = f"{parent_var}_rect_{index}"
        self.jsx_code.extend([
            f"var {rect_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Rect');",
        ])

        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{rect_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_transform_in_group(self, item: Dict, parent_var: str, index: int):
        """Process transform in group"""
        transform_var = f"{parent_var}_transform"
        self.jsx_code.append(f"var {transform_var} = {parent_var}.property('Transform');")

        # Set transform properties
        if 'p' in item:
            self.set_property_keyframes(f"{transform_var}.property('Position')", item['p'])
        if 's' in item:
            self.set_property_keyframes(f"{transform_var}.property('Scale')", item['s'])
        if 'r' in item:
            self.set_property_keyframes(f"{transform_var}.property('Rotation')", item['r'])
        if 'o' in item:
            self.set_property_keyframes(f"{transform_var}.property('Opacity')", item['o'])

    def process_shape_group(self, shape: Dict, layer_var: str, index: int):
        """Process shape group"""
        group_name = shape.get('nm', f'Group {index+1}')
        group_var = f"{layer_var}_group_{index}"

        self.jsx_code.extend([
            f"// Shape Group: {group_name}",
            f"var {group_var} = {layer_var}.property('Contents').addProperty('ADBE Vector Group');",
            f"{group_var}.name = '{group_name}';",
        ])

        # Process items in group
        if 'it' in shape:
            for item_index, item in enumerate(shape['it']):
                self.process_shape_item(item, group_var, item_index)

    def process_shape_item(self, item: Dict, parent_var: str, index: int):
        """Process individual shape items"""
        item_type = item.get('ty', '')

        if item_type == 'sh':  # Path
            self.process_path_in_group(item, parent_var, index)
        elif item_type == 'el':  # Ellipse
            self.process_ellipse_in_group(item, parent_var, index)
        elif item_type == 'rc':  # Rectangle
            self.process_rectangle_in_group(item, parent_var, index)
        elif item_type == 'fl':  # Fill
            self.process_fill_in_group(item, parent_var, index)
        elif item_type == 'st':  # Stroke
            self.process_stroke_in_group(item, parent_var, index)
        elif item_type == 'tm':  # Trim
            self.process_trim_in_group(item, parent_var, index)
        elif item_type == 'tr':  # Transform
            self.process_transform_in_group(item, parent_var, index)

    def process_path_in_group(self, item: Dict, parent_var: str, index: int):
        """Process path shape in group"""
        path_var = f"{parent_var}_path_{index}"

        self.jsx_code.extend([
            f"var {path_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Group');",
        ])

        # Set path data
        if 'ks' in item and 'k' in item['ks']:
            path_data = item['ks']['k']
            if isinstance(path_data, dict):
                # Static path
                self.set_static_path(path_var, path_data)
            elif isinstance(path_data, list):
                # Animated path
                self.set_animated_path(path_var, path_data)

    def process_ellipse_in_group(self, item: Dict, parent_var: str, index: int):
        """Process ellipse shape in group"""
        ellipse_var = f"{parent_var}_ellipse_{index}"

        self.jsx_code.extend([
            f"var {ellipse_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Shape - Ellipse');",
        ])

        # Set ellipse size
        if 's' in item and 'k' in item['s']:
            size_data = item['s']['k']
            if isinstance(size_data, list) and len(size_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Size').setValue([{size_data[0]}, {size_data[1]}]);")

        # Set ellipse position
        if 'p' in item and 'k' in item['p']:
            pos_data = item['p']['k']
            if isinstance(pos_data, list) and len(pos_data) >= 2:
                self.jsx_code.append(f"{ellipse_var}.property('Position').setValue([{pos_data[0]}, {pos_data[1]}]);")

    def process_fill_in_group(self, item: Dict, parent_var: str, index: int):
        """Process fill in group"""
        fill_var = f"{parent_var}_fill_{index}"

        self.jsx_code.extend([
            f"var {fill_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Fill');",
        ])

        # Set fill color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{fill_var}.property('Color').setValue({color_str});")

        # Set fill opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{fill_var}.property('Opacity').setValue({opacity_data});")

    def process_stroke_in_group(self, item: Dict, parent_var: str, index: int):
        """Process stroke in group"""
        stroke_var = f"{parent_var}_stroke_{index}"

        self.jsx_code.extend([
            f"var {stroke_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Graphic - Stroke');",
        ])

        # Set stroke color
        if 'c' in item and 'k' in item['c']:
            color_data = item['c']['k']
            if isinstance(color_data, list) and len(color_data) >= 3:
                color_str = f"[{color_data[0]:.6f}, {color_data[1]:.6f}, {color_data[2]:.6f}]"
                self.jsx_code.append(f"{stroke_var}.property('Color').setValue({color_str});")

        # Set stroke width
        if 'w' in item and 'k' in item['w']:
            width_data = item['w']['k']
            if isinstance(width_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Stroke Width').setValue({width_data});")

        # Set stroke opacity
        if 'o' in item and 'k' in item['o']:
            opacity_data = item['o']['k']
            if isinstance(opacity_data, (int, float)):
                self.jsx_code.append(f"{stroke_var}.property('Opacity').setValue({opacity_data});")

    def process_trim_in_group(self, item: Dict, parent_var: str, index: int):
        """Process trim paths in group"""
        trim_var = f"{parent_var}_trim_{index}"

        self.jsx_code.extend([
            f"var {trim_var} = {parent_var}.property('Contents').addProperty('ADBE Vector Filter - Trim');",
        ])

        # Set trim start
        if 's' in item:
            self.set_property_keyframes(f"{trim_var}.property('Start')", item['s'])

        # Set trim end
        if 'e' in item:
            self.set_property_keyframes(f"{trim_var}.property('End')", item['e'])

        # Set trim offset
        if 'o' in item:
            self.set_property_keyframes(f"{trim_var}.property('Offset')", item['o'])

    def set_static_path(self, path_var: str, path_data: Dict):
        """Set static path data"""
        if 'v' in path_data and 'i' in path_data and 'o' in path_data:
            vertices = path_data['v']
            in_tangents = path_data['i']
            out_tangents = path_data['o']
            closed = path_data.get('c', False)

            self.jsx_code.extend([
                f"var pathShape = new Shape();",
                f"pathShape.vertices = {vertices};",
                f"pathShape.inTangents = {in_tangents};",
                f"pathShape.outTangents = {out_tangents};",
                f"pathShape.closed = {str(closed).lower()};",
                f"{path_var}.property('Path').setValue(pathShape);",
            ])

    def set_animated_path(self, path_var: str, keyframes: List[Dict]):
        """Set animated path data with improved handling"""
        self.jsx_code.append(f"// Animated path with {len(keyframes)} keyframes")

        # Process all keyframes
        for i, kf in enumerate(keyframes):
            time = kf.get('t', 0)
            if 's' in kf:
                path_data = kf['s']
                if isinstance(path_data, list) and len(path_data) > 0:
                    # Handle case where 's' contains an array of path data
                    path_data = path_data[0]

                if isinstance(path_data, dict) and 'v' in path_data:
                    vertices = path_data['v']
                    in_tangents = path_data.get('i', [])
                    out_tangents = path_data.get('o', [])
                    closed = path_data.get('c', False)

                    self.jsx_code.extend([
                        f"var pathShape_{i} = new Shape();",
                        f"pathShape_{i}.vertices = {vertices};",
                        f"pathShape_{i}.inTangents = {in_tangents};",
                        f"pathShape_{i}.outTangents = {out_tangents};",
                        f"pathShape_{i}.closed = {str(closed).lower()};",
                        f"{path_var}.property('Path').setValueAtTime({time}/frameRate, pathShape_{i});",
                    ])
                else:
                    self.jsx_code.append(f"// Skipping keyframe {i} - invalid path data structure")
            else:
                self.jsx_code.append(f"// Skipping keyframe {i} - no 's' data")

    def set_property_keyframes(self, property_path: str, property_data: Dict):
        """Set keyframes for any property with smart keyframe detection"""
        # Skip if property_data is actually a list of raw keyframe objects
        if isinstance(property_data, list):
            # Check if it's a list of keyframe objects
            if len(property_data) > 0 and isinstance(property_data[0], dict) and 't' in property_data[0]:
                self.jsx_code.append(f"// Processing {len(property_data)} raw keyframes for {property_path}")
                for kf in property_data:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)

                    # Handle value properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            value_str = str(value[0])
                        else:
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
            else:
                self.jsx_code.append(f"// Skipping invalid keyframe data for {property_path}")
            return

        # SMART KEYFRAME DETECTION - Check for keyframes regardless of 'a' flag
        if 'k' in property_data:
            k_data = property_data['k']

            # Smart detection: if 'k' contains a list with keyframe objects, treat as animated
            if isinstance(k_data, list) and len(k_data) > 0:
                # Check if first item looks like a keyframe (has 't' and 's')
                first_item = k_data[0]
                if isinstance(first_item, dict) and 't' in first_item and 's' in first_item:
                    # This is definitely a keyframe array - process all keyframes
                    self.jsx_code.append(f"// Smart detection: {len(k_data)} keyframes for {property_path}")

                    for kf in k_data:
                        time = kf.get('t', 0)
                        value = kf.get('s', 0)

                        # Handle value properly - NEVER output keyframe objects
                        if isinstance(value, list):
                            if len(value) == 1:
                                # Single value in array - extract it
                                value_str = str(value[0])
                            else:
                                # Multiple values - keep as array
                                value_str = f"[{', '.join(map(str, value))}]"
                        else:
                            value_str = str(value)

                        self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")
                    return

        # Handle static values and other cases
        if 'k' in property_data:
            k_data = property_data['k']

            # Not keyframes, treat as static array value
            if isinstance(k_data, list):
                if len(k_data) == 1:
                    # Single value in array - extract it for single-dimension properties
                    if 'Position' in property_path and ('X Position' in property_path or 'Y Position' in property_path):
                        value_str = str(k_data[0])
                    else:
                        value_str = f"[{', '.join(map(str, k_data))}]"
                else:
                    # Multiple values - keep as array
                    value_str = f"[{', '.join(map(str, k_data))}]"

                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Single keyframe object in 'k' - extract the value
            elif isinstance(k_data, dict) and 's' in k_data:
                # This is a keyframe object, extract the value
                value = k_data['s']
                if isinstance(value, list):
                    if len(value) == 1:
                        value_str = str(value[0])
                    else:
                        value_str = f"[{', '.join(map(str, value))}]"
                else:
                    value_str = str(value)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

            # Regular static value
            else:
                value_str = str(k_data)
                self.jsx_code.append(f"{property_path}.setValue({value_str});")
                return

        # Fallback: check legacy 'a' flag method (for compatibility)
        if 'a' in property_data and property_data['a'] == 1:
            # Animated property with explicit flag
            if 'k' in property_data and isinstance(property_data['k'], list):
                keyframes = property_data['k']
                self.jsx_code.append(f"// Legacy animated: {len(keyframes)} keyframes for {property_path}")

                for kf in keyframes:
                    time = kf.get('t', 0)
                    value = kf.get('s', 0)

                    # Handle single values vs arrays properly - NEVER output keyframe objects
                    if isinstance(value, list):
                        if len(value) == 1:
                            # Single value in array - extract it
                            value_str = str(value[0])
                        else:
                            # Multiple values - keep as array
                            value_str = f"[{', '.join(map(str, value))}]"
                    else:
                        value_str = str(value)

                    self.jsx_code.append(f"{property_path}.setValueAtTime({time}/frameRate, {value_str});")

        # If we get here, no valid data was found
        self.jsx_code.append(f"// No valid keyframe data found for {property_path}")

    def set_layer_properties(self, layer: Dict, layer_var: str):
        """Set common layer properties"""
        # Set layer timing
        if 'ip' in layer:
            in_point = layer['ip'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.inPoint = {in_point:.6f};")

        if 'op' in layer:
            out_point = layer['op'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.outPoint = {out_point:.6f};")

        if 'st' in layer:
            start_time = layer['st'] / 24.0  # Convert frames to seconds
            self.jsx_code.append(f"{layer_var}.startTime = {start_time:.6f};")

        # Set layer visibility
        if 'hd' in layer and layer['hd']:
            self.jsx_code.append(f"{layer_var}.enabled = false;")

        # Set blend mode
        if 'bm' in layer and layer['bm'] != 0:
            blend_mode = self.get_blend_mode(layer['bm'])
            self.jsx_code.append(f"{layer_var}.blendingMode = {blend_mode};")

        # Set parent
        if 'parent' in layer:
            parent_index = layer['parent']
            if parent_index in self.layer_references:
                parent_var = self.layer_references[parent_index]
                self.jsx_code.append(f"{layer_var}.parent = {parent_var};")
            else:
                self.jsx_code.append(f"// Warning: Parent layer {parent_index} not found for {layer_var}")

        # Track matte setup is handled centrally after all layers are created
        if 'td' in layer and layer['td'] == 1:
            # This layer is a track matte (provides the matte)
            self.jsx_code.append(f"// {layer_var} is a track matte")
            # IMPORTANT: Hide track matte layers immediately
            self.jsx_code.append(f"{layer_var}.enabled = false; // Hide matte layer")
        elif 'tt' in layer:
            # This layer uses a track matte - will be set up later
            self.jsx_code.append(f"// {layer_var} uses track matte - will be configured later")

        # Set transform properties
        if 'ks' in layer:
            self.set_transform_properties(layer['ks'], layer_var)

        # Set effects
        if 'ef' in layer:
            self.process_effects(layer['ef'], layer_var)

    def set_transform_properties(self, transform: Dict, layer_var: str):
        """Set layer transform properties"""
        transform_group = f"{layer_var}.property('Transform')"

        # Opacity
        if 'o' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Opacity')", transform['o'])

        # Position - ALWAYS use combined position for After Effects compatibility
        if 'p' in transform:
            pos_data = transform['p']

            # Check if position has separate dimensions in the JSON
            if 's' in pos_data and pos_data['s'] == 1:
                # Separate dimensions in JSON - need to combine them for AE
                self.jsx_code.append(f"// Position has separate dimensions - combining for AE")

                # Extract X and Y keyframes and combine them
                x_keyframes = []
                y_keyframes = []

                if 'x' in pos_data and 'k' in pos_data['x']:
                    x_data = pos_data['x']['k']
                    if isinstance(x_data, list):
                        x_keyframes = x_data
                    else:
                        x_keyframes = [{'t': 0, 's': x_data}]

                if 'y' in pos_data and 'k' in pos_data['y']:
                    y_data = pos_data['y']['k']
                    if isinstance(y_data, list):
                        y_keyframes = y_data
                    else:
                        y_keyframes = [{'t': 0, 's': y_data}]

                # Combine keyframes by time
                combined_keyframes = {}

                # Get static Y value if Y is not animated
                static_y_value = 0
                if len(y_keyframes) == 1:
                    y_value = y_keyframes[0].get('s', 0)
                    if isinstance(y_value, list) and len(y_value) > 0:
                        static_y_value = y_value[0]
                    else:
                        static_y_value = y_value

                # Get static X value if X is not animated
                static_x_value = 0
                if len(x_keyframes) == 1:
                    x_value = x_keyframes[0].get('s', 0)
                    if isinstance(x_value, list) and len(x_value) > 0:
                        static_x_value = x_value[0]
                    else:
                        static_x_value = x_value

                for kf in x_keyframes:
                    time = kf.get('t', 0)
                    if time not in combined_keyframes:
                        # Initialize with static Y value if Y is not animated
                        y_val = static_y_value if len(y_keyframes) <= 1 else 0
                        combined_keyframes[time] = {'t': time, 's': [0, y_val]}

                    # Extract value properly - handle arrays and single values
                    x_value = kf.get('s', 0)
                    if isinstance(x_value, list) and len(x_value) > 0:
                        x_value = x_value[0]  # Extract from array
                    combined_keyframes[time]['s'][0] = x_value

                for kf in y_keyframes:
                    time = kf.get('t', 0)
                    if time not in combined_keyframes:
                        # Initialize with static X value if X is not animated
                        x_val = static_x_value if len(x_keyframes) <= 1 else 0
                        combined_keyframes[time] = {'t': time, 's': [x_val, 0]}

                    # Extract value properly - handle arrays and single values
                    y_value = kf.get('s', 0)
                    if isinstance(y_value, list) and len(y_value) > 0:
                        y_value = y_value[0]  # Extract from array
                    combined_keyframes[time]['s'][1] = y_value

                # Fill in missing Y values for X keyframes when Y is static
                if len(y_keyframes) <= 1:
                    for time in combined_keyframes:
                        combined_keyframes[time]['s'][1] = static_y_value

                # Fill in missing X values for Y keyframes when X is static
                if len(x_keyframes) <= 1:
                    for time in combined_keyframes:
                        combined_keyframes[time]['s'][0] = static_x_value

                # Set combined keyframes
                for time in sorted(combined_keyframes.keys()):
                    kf = combined_keyframes[time]
                    pos_value = kf['s']
                    self.jsx_code.append(f"{transform_group}.property('Position').setValueAtTime({time}/frameRate, [{pos_value[0]}, {pos_value[1]}]);")

            else:
                # Combined position - use as-is
                self.set_property_keyframes(f"{transform_group}.property('Position')", pos_data)

        # Scale
        if 's' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Scale')", transform['s'])

        # Rotation
        if 'r' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Rotation')", transform['r'])

        # Anchor Point
        if 'a' in transform:
            self.set_property_keyframes(f"{transform_group}.property('Anchor Point')", transform['a'])

    def process_effects(self, effects: List[Dict], layer_var: str):
        """Process layer effects"""
        for i, effect in enumerate(effects):
            effect_type = effect.get('ty', 0)
            effect_name = effect.get('nm', f'Effect {i+1}')

            if effect_type == 5:  # Color Control
                self.add_color_control_effect(effect, layer_var, i)
            elif effect_type == 21:  # Fill
                self.add_fill_effect(effect, layer_var, i)
            elif effect_type == 29:  # Gaussian Blur
                self.add_gaussian_blur_effect(effect, layer_var, i)
            elif effect_type == 0:  # Slider Control
                self.add_slider_control_effect(effect, layer_var, i)
            elif effect_type == 3:  # Point Control
                self.add_point_control_effect(effect, layer_var, i)
            else:
                self.jsx_code.append(f"// Unknown effect type {effect_type}: {effect_name}")

    def add_color_control_effect(self, effect: Dict, layer_var: str, index: int):
        """Add color control effect"""
        effect_name = effect.get('nm', 'Color Control')
        self.jsx_code.extend([
            f"// Color Control Effect: {effect_name}",
            f"var effect_{index} = {layer_var}.property('Effects').addProperty('ADBE Color Control');",
            f"effect_{index}.name = '{effect_name}';",
        ])

        # Set color value if available - Color Control expects RGB array, not single values
        if 'ef' in effect and len(effect['ef']) > 0:
            color_prop = effect['ef'][0]
            if 'v' in color_prop:
                # Handle color control values specially - they should be RGB arrays
                color_data = color_prop['v']
                if 'k' in color_data:
                    value = color_data['k']
                    # Convert single values or position values to RGB color
                    if isinstance(value, (int, float)):
                        # Single number - convert to grayscale RGB
                        rgb_val = value / 255.0 if value > 1 else value
                        color_str = f"[{rgb_val:.6f}, {rgb_val:.6f}, {rgb_val:.6f}]"
                    elif isinstance(value, list):
                        if len(value) == 2:
                            # Position values - use as XY for RG, set B to 0
                            color_str = f"[{value[0]/255.0 if value[0] > 1 else value[0]:.6f}, {value[1]/255.0 if value[1] > 1 else value[1]:.6f}, 0]"
                        elif len(value) >= 3:
                            # Already RGB values
                            r = value[0]/255.0 if value[0] > 1 else value[0]
                            g = value[1]/255.0 if value[1] > 1 else value[1]
                            b = value[2]/255.0 if value[2] > 1 else value[2]
                            color_str = f"[{r:.6f}, {g:.6f}, {b:.6f}]"
                        else:
                            # Single value in array
                            rgb_val = value[0]/255.0 if value[0] > 1 else value[0]
                            color_str = f"[{rgb_val:.6f}, {rgb_val:.6f}, {rgb_val:.6f}]"
                    else:
                        # Default to white
                        color_str = "[1, 1, 1]"

                    self.jsx_code.append(f"effect_{index}.property('Color').setValue({color_str});")

    def add_fill_effect(self, effect: Dict, layer_var: str, index: int):
        """Add fill effect"""
        effect_name = effect.get('nm', 'Fill')
        self.jsx_code.extend([
            f"// Fill Effect: {effect_name}",
            f"var effect_{index} = {layer_var}.property('Effects').addProperty('ADBE Fill');",
            f"effect_{index}.name = '{effect_name}';",
        ])

    def add_gaussian_blur_effect(self, effect: Dict, layer_var: str, index: int):
        """Add gaussian blur effect"""
        effect_name = effect.get('nm', 'Gaussian Blur')
        self.jsx_code.extend([
            f"// Gaussian Blur Effect: {effect_name}",
            f"var effect_{index} = {layer_var}.property('Effects').addProperty('ADBE Gaussian Blur 2');",
            f"effect_{index}.name = '{effect_name}';",
        ])

        # Set blur amount if available
        if 'ef' in effect and len(effect['ef']) > 0:
            blur_prop = effect['ef'][0]
            if 'v' in blur_prop:
                blur_data = blur_prop['v']
                if 'k' in blur_data:
                    blur_value = blur_data['k']
                    if isinstance(blur_value, (int, float)):
                        self.jsx_code.append(f"effect_{index}.property('Blurriness').setValue({blur_value});")

    def add_slider_control_effect(self, effect: Dict, layer_var: str, index: int):
        """Add slider control effect"""
        effect_name = effect.get('nm', 'Slider Control')
        self.jsx_code.extend([
            f"// Slider Control Effect: {effect_name}",
            f"var effect_{index} = {layer_var}.property('Effects').addProperty('ADBE Slider Control');",
            f"effect_{index}.name = '{effect_name}';",
        ])

        # Set slider value if available
        if 'ef' in effect and len(effect['ef']) > 0:
            slider_prop = effect['ef'][0]
            if 'v' in slider_prop:
                self.set_property_keyframes(f"effect_{index}.property('Slider')", slider_prop['v'])

    def add_point_control_effect(self, effect: Dict, layer_var: str, index: int):
        """Add point control effect"""
        effect_name = effect.get('nm', 'Point Control')
        self.jsx_code.extend([
            f"// Point Control Effect: {effect_name}",
            f"var effect_{index} = {layer_var}.property('Effects').addProperty('ADBE Point Control');",
            f"effect_{index}.name = '{effect_name}';",
        ])

        # Set point value if available
        if 'ef' in effect and len(effect['ef']) > 0:
            point_prop = effect['ef'][0]
            if 'v' in point_prop:
                self.set_property_keyframes(f"effect_{index}.property('Point')", point_prop['v'])

    def get_blend_mode(self, bm_value: int) -> str:
        """Convert blend mode value to AE constant"""
        blend_modes = {
            0: "BlendingMode.NORMAL",
            1: "BlendingMode.MULTIPLY",
            2: "BlendingMode.SCREEN",
            3: "BlendingMode.OVERLAY",
            # Add more as needed
        }
        return blend_modes.get(bm_value, "BlendingMode.NORMAL")

    def get_track_matte_type(self, tt_value: int) -> str:
        """Convert track matte type to AE constant"""
        matte_types = {
            1: "TrackMatteType.ALPHA",
            2: "TrackMatteType.ALPHA_INVERTED",
            3: "TrackMatteType.LUMA",
            4: "TrackMatteType.LUMA_INVERTED",
        }
        return matte_types.get(tt_value, "TrackMatteType.NO_TRACK_MATTE")

    def setup_track_mattes(self, layers: List[Dict]):
        """Set up track matte relationships after all layers are created"""
        self.jsx_code.append("// Set up track matte relationships")

        # Find all track matte relationships
        track_matte_pairs = []
        for layer in layers:
            if 'tt' in layer and 'tp' in layer:  # Layer uses track matte
                layer_index = layer.get('ind', 0)
                matte_index = layer.get('tp', 0)
                matte_type = layer.get('tt', 0)
                track_matte_pairs.append((matte_index, layer_index, matte_type))

        # Set up track matte relationships
        for matte_index, layer_index, matte_type in track_matte_pairs:
            matte_var = f"layer_{matte_index}"
            layer_var = f"layer_{layer_index}"
            matte_type_str = self.get_track_matte_type(matte_type)

            self.jsx_code.extend([
                f"// Set up track matte: {layer_var} uses {matte_var} as {matte_type_str}",
                f"try {{",
                f"    // Ensure track matte layer is directly above the layer that uses it",
                f"    {matte_var}.moveBefore({layer_var});",
                f"    // Set the track matte type",
                f"    {layer_var}.trackMatteType = {matte_type_str};",
                f"    // IMPORTANT: Hide the matte layer (turn off visibility)",
                f"    {matte_var}.enabled = false;",
                f"}} catch(e) {{",
                f"    // Track matte setup failed for {layer_var}",
                f"    alert('Track matte setup failed for {layer_var}: ' + e.toString());",
                f"}}",
            ])

        if track_matte_pairs:
            self.jsx_code.append("")

    @staticmethod
    def find_json_files() -> List[str]:
        """Find all JSON files in the current directory"""
        json_files = []
        for file in os.listdir('.'):
            if file.lower().endswith('.json'):
                json_files.append(file)
        return sorted(json_files)

    @staticmethod
    def interactive_file_selection() -> Optional[str]:
        """Interactive file selection with enhanced user experience"""
        print("🎬 INTELLIGENT LOTTIE TO JSX CONVERTER")
        print("=" * 50)
        print()

        # Find JSON files
        json_files = LottieToJSXConverter.find_json_files()

        if not json_files:
            print("❌ No JSON files found in the current directory.")
            print("💡 Please ensure your Lottie/Bodymovin JSON files are in this folder.")
            return None

        print(f"📁 Found {len(json_files)} JSON file(s):")
        print()

        # Display files with analysis
        for i, file in enumerate(json_files, 1):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                name = data.get('nm', 'Unnamed')
                width = data.get('w', 0)
                height = data.get('h', 0)
                duration = data.get('op', 0) / data.get('fr', 24)
                layers = len(data.get('layers', []))
                assets = len(data.get('assets', []))

                print(f"  {i}. {file}")
                print(f"     📋 Name: {name}")
                print(f"     📐 Size: {width}x{height}")
                print(f"     ⏱️  Duration: {duration:.1f}s")
                print(f"     🎭 Layers: {layers}")
                print(f"     📦 Assets: {assets}")
                print()

            except Exception as e:
                print(f"  {i}. {file} (⚠️  Error reading: {str(e)})")
                print()

        # Get user selection
        while True:
            try:
                choice = input(f"🔍 Select a file to convert (1-{len(json_files)}) or 'q' to quit: ").strip()

                if choice.lower() == 'q':
                    print("👋 Goodbye!")
                    return None

                index = int(choice) - 1
                if 0 <= index < len(json_files):
                    selected_file = json_files[index]
                    print(f"✅ Selected: {selected_file}")
                    return selected_file
                else:
                    print(f"❌ Please enter a number between 1 and {len(json_files)}")

            except ValueError:
                print("❌ Please enter a valid number or 'q' to quit")
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                return None

    def analyze_and_convert_with_smart_prompts(self, json_file: str) -> bool:
        """Analyze JSON file and convert with intelligent user guidance"""
        print(f"\n🔍 ANALYZING: {json_file}")
        print("-" * 40)

        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"❌ Error reading JSON file: {e}")
            return False

        # Analyze assets
        assets = data.get('assets', [])
        external_assets = []

        for asset in assets:
            if 'u' in asset and 'p' in asset:
                asset_type = 'unknown'
                if 'e' in asset:
                    if asset['e'] == 0:
                        asset_type = 'image'
                    elif asset['e'] == 1:
                        asset_type = 'video'
                elif 't' in asset and asset['t'] == 2:
                    asset_type = 'audio'

                if asset_type != 'unknown':
                    external_assets.append({
                        'id': asset.get('id', ''),
                        'name': asset.get('nm', ''),
                        'type': asset_type,
                        'path': asset.get('u', '') + asset.get('p', ''),
                        'width': asset.get('w', 0),
                        'height': asset.get('h', 0)
                    })

        # Show analysis results
        print(f"📊 ANALYSIS RESULTS:")
        print(f"   • Animation: {data.get('nm', 'Unnamed')}")
        print(f"   • Dimensions: {data.get('w', 0)}x{data.get('h', 0)}")
        print(f"   • Duration: {data.get('op', 0) / data.get('fr', 24):.1f} seconds")
        print(f"   • Layers: {len(data.get('layers', []))}")
        print(f"   • External Assets: {len(external_assets)}")

        if external_assets:
            print(f"\n📦 EXTERNAL ASSETS DETECTED:")
            for asset in external_assets:
                print(f"   • {asset['name']} ({asset['type']}) - {asset['path']}")
                if asset['width'] and asset['height']:
                    print(f"     Size: {asset['width']}x{asset['height']}")

        print(f"\n💡 CONVERSION NOTES:")
        print(f"   • Missing assets will prompt file dialogs during JSX execution")
        print(f"   • You can browse and select replacement files")
        print(f"   • Placeholders will be created for skipped assets")

        # Generate output filename
        base_name = os.path.splitext(json_file)[0]
        output_file = f"{base_name}_converted.jsx"

        print(f"\n🚀 CONVERTING...")
        print(f"   Input:  {json_file}")
        print(f"   Output: {output_file}")

        # Convert
        try:
            jsx_script = self.convert_json_to_jsx(json_file, output_file)
            print(f"\n✅ CONVERSION SUCCESSFUL!")
            print(f"   📄 Generated: {output_file}")
            print(f"   📏 Lines: {jsx_script.count(chr(10)) + 1:,}")
            return True

        except Exception as e:
            print(f"\n❌ CONVERSION FAILED: {e}")
            return False

    def add_footer(self, data: Dict):
        """Add JSX script footer"""
        name = data.get('nm', 'Animation')
        duration = data.get('op', 120) / data.get('fr', 24)
        layer_count = len(data.get('layers', []))
        asset_count = len(data.get('assets', []))

        self.jsx_code.extend([
            "",
            "// Animation creation complete",
            f"alert('Animation \"{name}\" created successfully!\\n' +",
            f"      'Duration: {duration:.2f} seconds\\n' +",
            f"      'Layers: {layer_count}\\n' +",
            f"      'Assets: {asset_count}');",
            "",
            "app.endUndoGroup();",
        ])

def main():
    """Main function for interactive conversion"""
    try:
        converter = LottieToJSXConverter()

        # Interactive file selection
        selected_file = converter.interactive_file_selection()

        if selected_file:
            # Analyze and convert with smart prompts
            success = converter.analyze_and_convert_with_smart_prompts(selected_file)

            if success:
                print(f"\n🎉 READY TO USE!")
                print(f"   1. Open After Effects")
                print(f"   2. Go to File > Scripts > Run Script File...")
                print(f"   3. Select the generated .jsx file")
                print(f"   4. Follow the smart file dialogs for missing assets")
                print(f"\n🎬 Your animation will be created with intelligent asset handling!")
            else:
                print(f"\n� Conversion failed. Please check the JSON file and try again.")

    except KeyboardInterrupt:
        print(f"\n� Conversion cancelled by user.")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print(f"Please check your JSON file and try again.")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# ======================================================================
#  UNIVERSAL LOTTIE → AE JSX  (robust, lazy-build, error-safe)
#  ------------------------------------------------------------------
#  • All comps + lazy creation (no “KeyError: refId” any more)
#  • Solids, Nulls, Text (basic animators), Shape-layers (full tree)
#  • Effects, Expressions, Layer-styles, Masks, Track-mattes, Parenting
#  • Duration clamped ≥ 1 frame   → never “value 0 out of range”
#
#  USAGE  (quote paths in PowerShell!)
#     python lottie_to_jsx_universal.py "in.json" "out.jsx"
# ======================================================================

import json, sys, re, pathlib

FPS_FALLBACK = 30
MIN_FRAMES   = 1   # comp must be ≥ one frame


def _safe(name:str, used:set)->str:
    base = re.sub(r'[^A-Za-z0-9_]', '_', name)
    if not re.match(r'[A-Za-z_]', base): base = '_' + base
    out, i = base, 2
    while out in used: out, i = f'{base}_{i}', i+1
    used.add(out); return out


class JSX:
    def __init__(self):
        self.lines, self.ind, self.used = [], 0, set()
    def add(self, l=''): self.lines.append('    '*self.ind + l)
    def var(self, label): return _safe(label, self.used)
    def write(self, path): pathlib.Path(path).write_text('\n'.join(self.lines), 'utf-8')


class Conv:
    def __init__(self, jd, x):
        self.j, self.x = jd, x
        self.comp_var, self.fps = {}, {}
        # quick lookup for any asset by id
        self.assets = {a['id']: a for a in jd.get('assets', []) if 'id' in a}
        self._run()

    # — orchestration —
    def _run(self):
        self.x.add("// universal rebuild")
        self.x.add("app.beginUndoGroup('Lottie→JSX');")
        self.x.add("var proj = app.project || app.newProject();\n")
        # eager-build assets that are true comps
        for aid, a in self.assets.items():
            if 'layers' in a: self._ensure_comp(aid)
        # root
        self._ensure_comp(self.j.get('id','root'), root_json=self.j, make_root=True)
        self.x.add("app.endUndoGroup();")

    # — ensure comp exists —
    def _ensure_comp(self, cid, *, root_json=None, make_root=False):
        if cid in self.comp_var:
            return self.comp_var[cid]
        cj = root_json if root_json else self.assets.get(cid)
        if not cj:
            # missing asset – create 1-frame placeholder
            cvar = self.x.var(f'missing_{cid}')
            self.x.add(f"var {cvar} = proj.items.addComp('{cid}_MISSING',1920,1080,1,1/30,30);")
            self.comp_var[cid] = cvar; self.fps[cid] = 30
            return cvar
        # build new
        cvar = self.x.var(f'comp_{cid}')
        fps  = cj.get('fr', FPS_FALLBACK)
        dur  = max((cj.get('op',0)-cj.get('ip',0))/fps, MIN_FRAMES/fps)
        w,h  = cj.get('w',1920), cj.get('h',1080)
        self.x.add(f"var {cvar}=proj.items.addComp('{cj.get('nm',cid)}',{w},{h},1,{dur},{fps});")
        self.comp_var[cid], self.fps[cid] = cvar, fps

        idx_map={}
        for lyr in cj.get('layers', []):
            idx_map[lyr['ind']] = self._mk_layer(lyr, cvar, fps)
        for lyr in cj.get('layers', []):
            if 'parent' in lyr:
                self.x.add(f"{cvar}.layer({idx_map[lyr['ind']]})"
                           f".parent={cvar}.layer({idx_map[lyr['parent']]});")
        if make_root: self.x.add(f'proj.activeItem={cvar};')
        self.x.add('')
        return cvar

    # — layer —
    def _mk_layer(self, lj, cvar, fps):
        lv   = self.x.var(f'L{lj["ind"]}')
        typ  = lj['ty']; name = lj.get('nm','Layer')

        if typ==1:  # solid
            col=lj.get('sc','#ffffff'); r,g,b=[int(col[i:i+2],16)/255 for i in (1,3,5)]
            sw,sh=lj.get('sw',1920),lj.get('sh',1080)
            self.x.add(f"var {lv}={cvar}.layers.addSolid([{r},{g},{b}],'{name}',{sw},{sh},1);")
        elif typ==0: # pre-comp
            ref=self._ensure_comp(lj['refId'])
            self.x.add(f"var {lv}={cvar}.layers.add({ref});")
        elif typ==5: # text
            text=(lj.get('t',{}).get('d',{}).get('k',[{}])[0].get('s',{}).get('t',''))
            safe=text.replace('\\','\\\\').replace(\"'\",\"\\\\'\")
            self.x.add(f"var {lv}={cvar}.layers.addText('{safe}');")
            self._text_anim(lj,lv,fps)
        elif typ==4: # shape
            self.x.add(f"var {lv}={cvar}.layers.addShape();")
            self._shape(lj,lv,fps)
        else:       # null
            self.x.add(f"var {lv}={cvar}.layers.addNull(); {lv}.name='{name}';")

        self._transform(lj.get('ks',{}),lv,fps)
        self._masks(lj,lv,fps)
        self._track(lj,lv)
        self._effects(lj,lv,fps)
        self._styles(lj,lv,fps)
        return lj['ind']+1

    # — transforms —
    def _transform(self,ks,lv,fps):
        mp={'p':'Position','s':'Scale','r':'Rotation','o':'Opacity','a':'Anchor Point'}
        for k,pn in mp.items():
            if k in ks: self._prop(f\"{lv}.property('Transform').property('{pn}')\",ks[k],fps)

    # — generic prop —
    def _prop(self,path,pj,fps):
        if not isinstance(pj,dict) or 'k' not in pj:
            self.x.add(f'{path}.setValue({pj});'); return
        k=pj['k']
        if not isinstance(k,list) or (k and not isinstance(k[0],dict)):
            self.x.add(f'{path}.setValue({k});'); return
        for key in k:
            t=key['t']/fps; v=key['s'][0] if isinstance(key['s'],list) and len(key['s'])==1 else key['s']
            self.x.add(f'{path}.setValueAtTime({t},{v});')
            if 'i'in key and 'o'in key:
                self.x.add(f\"(function(){{var ki={path}.nearestKeyIndex({t});{path}.setTemporalEaseAtKey(ki,{key['i']},{key['o']});}})();\")

    # — effects —
    def _effects(self,lj,lv,fps):
        if not lj.get('ef'): return
        self.x.add(f\"var effPar={lv}.property('ADBE Effect Parade');\")
        for i,ef in enumerate(lj['ef']):
            ev=self.x.var(f'ef{i}')
            self.x.add(f\"var {ev}=effPar.addProperty('{ef['mn']}'); {ev}.name='{ef.get('nm',ef['mn'])}';\")
            for p in ef.get('ef',[]):
                pp=f\"{ev}.property('{p['mn']}')\"
                if p.get('expen'):
                    e=p['exp'].replace('\\\\','\\\\\\\\').replace(\"'\",\"\\\\'\")
                    self.x.add(f\"{pp}.expression='{e}';\")
                else: self._prop(pp,p.get('v',{}),fps)

    # — styles —
    def _styles(self,lj,lv,fps):
        if not lj.get('sy'): return
        self.x.add(f\"var ls={lv}.property('ADBE Layer Styles');\")
        for sty in lj['sy']:
            sv=self.x.var('sty')
            self.x.add(f\"var {sv}=ls.addProperty('ADBE {sty['nm']}');\")
            for k,v in sty.items():
                if k in ('nm','ty'): continue
                self._prop(f\"{sv}.property('{k}')\",v,fps)

    # — masks —
    def _masks(self,lj,lv,fps):
        for m in lj.get('masksProperties',[]):
            mv=self.x.var('mask')
            self.x.add(f\"var {mv}={lv}.Masks.addProperty('Mask'); {mv}.name='{m.get('nm','Mask')}';\")
            self._prop(f\"{mv}.property('maskShape')\",m['pt'],fps)
            md={0:'ADD',1:'SUBTRACT',2:'NONE',3:'INTERSECT',4:'LIGHTEN',5:'DARKEN',6:'DIFFERENCE'}
            self.x.add(f\"{mv}.maskMode=MaskMode.{md.get(m.get('mode',0))};\")
            if 'o' in m: self._prop(f\"{mv}.property('maskOpacity')\",m['o'],fps)

    # — matte —
    def _track(self,lj,lv):
        if 'tt' not in lj: return
        mt={1:'ALPHA',2:'ALPHA_INVERTED',3:'LUMA',4:'LUMA_INVERTED'}
        self.x.add(f\"{lv}.trackMatteType=TrackMatteType.{mt[lj['tt']]};\")

    # — text anim (basic) —
    def _text_anim(self,lj,lv,fps):
        for a in lj.get('t',{}).get('a',[]):
            av=self.x.var('ta')
            self.x.add(f\"var {av}={lv}.property('ADBE Text Animators').addProperty('ADBE Text Animator');\")
            for k,v in a.get('s',{}).items(): self._prop(f\"{av}.property('{k}')\",v,fps)
            for sel in a.get('r',[]):
                sv=self.x.var('sel')
                self.x.add(f\"var {sv}={av}.property('ADBE Text Selectors').addProperty('ADBE Text Selector');\")
                self._prop(f\"{sv}.property('ADBE Text Selector Range Start')\",sel['s'],fps)
                self._prop(f\"{sv}.property('ADBE Text Selector Range End')\",sel['e'],fps)

    # — shape walker (condensed) —
    def _shape(self,lj,lv,fps):
        def walk(arr,parent):
            for it in arr:
                ty=it['ty']
                if ty=='gr':
                    gv=self.x.var('grp')
                    self.x.add(f\"var {gv}={parent}.addProperty('ADBE Vector Group'); {gv}.name='{it.get('nm','Group')}';\")
                    walk(it.get('it',[]),f\"{gv}.property('ADBE Vector Group')\")
                    continue
                mapty={ 'sh':'ADBE Vector Shape - Group','rc':'ADBE Vector Shape - Rect',
                        'el':'ADBE Vector Shape - Ellipse','pol':'ADBE Vector Shape - Star',
                        'fl':'ADBE Vector Graphic - Fill','st':'ADBE Vector Graphic - Stroke',
                        'gf':'ADBE Vector Graphic - G-Fill','gs':'ADBE Vector Graphic - G-Stroke',
                        'sr':'ADBE Vector Graphic - G-Stroke','tr':'ADBE Vector Transform Group',
                        'tm':'ADBE Vector Filter - Trim','rd':'ADBE Vector Filter - RC',
                        'rp':'ADBE Vector Filter - Repeater','op':'ADBE Vector Filter - Offset',
                        'zz':'ADBE Vector Filter - Zig Zag','ms':'ADBE Vector Filter - Merge',
                        'pb':'ADBE Vector Filter - PB' }.get(ty)
                if not mapty: continue
                vv=self.x.var(ty)
                self.x.add(f\"var {vv}={parent}.addProperty('{mapty}');\")
                if 'nm'in it: self.x.add(f\"{vv}.name='{it['nm']}';\")
                if ty in ('sh','rc','el','pol'):
                    if 'ks'in it: self._prop(f\"{vv}.property('Path')\",it['ks'],fps)
                    if 'p'in it:  self._prop(f\"{vv}.property('Size')\",it['p'],fps)
                if ty in ('fl','st','gf','gs','sr'):
                    if 'c'in it: self._prop(f\"{vv}.property('Color')\",it['c'],fps)
                    if 'o'in it: self._prop(f\"{vv}.property('Opacity')\",it['o'],fps)
                    if 'w'in it: self._prop(f\"{vv}.property('Stroke Width')\",it['w'],fps)
                if 'it' in it and ty!='gr': walk(it['it'],parent)
        walk(lj.get('shapes',[]),f\"{lv}.property('Contents')\")


def main():
    if len(sys.argv)!=3:
        sys.exit('Usage: python lottie_to_jsx_universal.py "input.json" "output.jsx"')
    data=json.loads(pathlib.Path(sys.argv[1]).read_text('utf-8'))
    js=JSX(); Conv(data,js); js.write(sys.argv[2])
    print('✅ JSX written to', sys.argv[2])

if __name__=='__main__': main()

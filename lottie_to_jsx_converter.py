#!/usr/bin/env python3
# ======================================================================
#  FULL LOTTIE → JSX RE-BUILDER
#  • Comps, Solids, Pre-comps, Nulls
#  • <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Parenting
#  • Layer Styles (sy)
#  • ALL Effects (matchName-driven)
#  • Text Layers + Animators
#  • Shape Layers (paths, fills, strokes, groups, repeaters, trim, etc.)
#  • Keyframes + Expressions everywhere
#
#  Usage:
#     python lottie_to_jsx_full.py in.json out.jsx
# ======================================================================

import json, sys, re, math, pathlib

FPS_FALLBACK = 30

# ────────────────────────────────────────────────────────────────────
# helpers
# ────────────────────────────────────────────────────────────────────
def _safe(name: str, used: set):
    base = re.sub(r'[^A-Za-z0-9_]', '_', name)
    if not re.match(r'[A-Za-z_]', base):
        base = '_' + base
    cand, i = base, 2
    while cand in used:
        cand = f"{base}_{i}"
        i += 1
    used.add(cand)
    return cand

class JSX:
    def __init__(self):
        self.lines, self.indent, self.used = [], 0, set()
    def add(self, s=""): self.lines.append("    "*self.indent + s)
    def var(self, label): return _safe(label, self.used)
    def write(self, p): pathlib.Path(p).write_text("\n".join(self.lines), "utf-8")

# ────────────────────────────────────────────────────────────────────
# converter
# ────────────────────────────────────────────────────────────────────
class Conv:
    def __init__(self, jd: dict, jsx: JSX):
        self.j, self.x = jd, jsx
        self.comp = {}      # id ➜ var
        self.fps  = {}      # id ➜ fps
        self._run()

    # main
    def _run(self):
        # Add JSON shim for After Effects (ECMAScript 3 doesn't have native JSON)
        self.x.add("// JSON shim for After Effects ExtendScript")
        self.x.add("if (typeof JSON === 'undefined') {")
        self.x.add("    JSON = {};")
        self.x.add("    JSON.stringify = function(obj) {")
        self.x.add("        if (obj === null) return 'null';")
        self.x.add("        if (typeof obj === 'string') return '\"' + obj.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"') + '\"';")
        self.x.add("        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);")
        self.x.add("        if (obj instanceof Array) {")
        self.x.add("            var arr = [];")
        self.x.add("            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));")
        self.x.add("            return '[' + arr.join(',') + ']';")
        self.x.add("        }")
        self.x.add("        if (typeof obj === 'object') {")
        self.x.add("            var pairs = [];")
        self.x.add("            for (var key in obj) {")
        self.x.add("                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));")
        self.x.add("            }")
        self.x.add("            return '{' + pairs.join(',') + '}';")
        self.x.add("        }")
        self.x.add("        return 'undefined';")
        self.x.add("    };")
        self.x.add("}")
        self.x.add("")
        self.x.add("// auto-generated full rebuild")
        self.x.add("app.beginUndoGroup('JSON-to-JSX FULL');")
        self.x.add("var proj=app.project||app.newProject();\n")

        # FIXED: Two-pass approach to handle composition dependencies
        # Pass 1: Create all composition shells (no layers yet)
        all_comps = []
        for a in self.j.get("assets", []):
            if "layers" in a:
                all_comps.append(a)
                self._create_comp_shell(a)
        all_comps.append(self.j)  # Add root comp
        self._create_comp_shell(self.j, root=True)

        # Pass 2: Add layers to all compositions (now all comp references exist)
        for comp_data in all_comps[:-1]:  # Asset comps first
            self._populate_comp_layers(comp_data)
        self._populate_comp_layers(self.j, root=True)  # Root comp last

        self.x.add("app.endUndoGroup();")

    # create comp shell (composition without layers)
    def _create_comp_shell(self, cj: dict, root=False):
        cid = cj.get("id", f"root_{id(cj)}")
        cvar= self.x.var(f"comp_{cid}")
        fps = cj.get("fr", FPS_FALLBACK)
        self.comp[cid]=cvar; self.fps[cid]=fps
        dur = (cj.get("op",0)-cj.get("ip",0))/fps
        # FIXED: After Effects requires duration > 0, minimum 1 frame
        if dur <= 0:
            dur = 1.0/fps  # Set to 1 frame duration
        w,h = cj.get("w",1920), cj.get("h",1080)
        comp_name = cj.get('nm',cid).replace("'", "\\'")  # Escape quotes
        self.x.add(f"var {cvar}=proj.items.addComp('{comp_name}',{w},{h},1,{dur:.6f},{fps});")
        # FIXED: Don't set activeItem as it's read-only. Instead, open comp in viewer if it's root
        if root: self.x.add(f"{cvar}.openInViewer();")
        self.x.add("")

    # populate comp with layers (after all comp shells exist)
    def _populate_comp_layers(self, cj: dict, root=False):
        cid = cj.get("id", f"root_{id(cj)}")
        cvar = self.comp[cid]
        fps = self.fps[cid]
        idx_map={}
        for l in cj.get("layers",[]):
            idx=self._mk_layer(l,cvar,fps)
            idx_map[l["ind"]]=idx
        # parenting pass
        for l in cj.get("layers",[]):
            if "parent" in l:
                self.x.add(f"{cvar}.layer({idx_map[l['ind']]}).parent={cvar}.layer({idx_map[l['parent']]});")
        self.x.add("")

    # layer
    def _mk_layer(self, lj:dict, cv:str, fps:float):
        ltype, lname = lj["ty"], lj.get("nm","Layer")
        lvar = self.x.var(f"L{lj['ind']}")
        if ltype==1: # solid
            col=lj.get("sc","#ffffff"); r,g,b=[int(col[i:i+2],16)/255 for i in (1,3,5)]
            sw,sh=lj.get("sw",1920),lj.get("sh",1080)
            self.x.add(f"var {lvar}={cv}.layers.addSolid([{r},{g},{b}],'{lname}',{sw},{sh},1);")
        elif ltype==0:
            self.x.add(f"var {lvar}={cv}.layers.add({self.comp[lj['refId']]});")
        elif ltype==5:
            txt=lj.get('t',{}).get('d',{}).get('k',[{}])[0].get('s',{}).get('t','')
            safe_txt = txt.replace('\\','\\\\').replace("'","\\'")
            self.x.add(f"var {lvar}={cv}.layers.addText('{safe_txt}');")
            self._text_anim(lj,lvar,fps)
        elif ltype==4:
            self.x.add(f"var {lvar}={cv}.layers.addShape();")
            self._shape(lj,lvar,fps)
        else:
            self.x.add(f"var {lvar}={cv}.layers.addNull(); {lvar}.name='{lname}';")
        # transforms
        self._transform(lj.get("ks",{}),lvar,fps)
        # masks & mattes
        self._masks(lj,lvar,fps)
        self._track(lj,lvar,cv)
        # effects
        self._effects(lj,lvar,fps)
        # layer styles
        self._styles(lj,lvar,fps)
        return lj["ind"]+1

    # transform
    def _transform(self,ks,lvar,fps):
        mp={"p":"Position","s":"Scale","r":"Rotation","o":"Opacity","a":"Anchor Point"}
        for k,p in mp.items():
            if k in ks: self._prop(f"{lvar}.property('Transform').property('{p}')",ks[k],fps)
    # generic property
    def _prop(self,path,pj,fps):
        # Handle simple values (numbers, strings, arrays) that aren't property objects
        if not isinstance(pj, dict) or "k" not in pj:
            # FIXED: Add error handling for properties that can't be set
            self.x.add(f"try {{ {path}.setValue({pj}); }} catch(e) {{ /* Property cannot be set: */ }}"); return
        k=pj["k"]
        if not isinstance(k,list) or (k and not isinstance(k[0],dict)):
            # FIXED: Add error handling for properties that can't be set
            self.x.add(f"try {{ {path}.setValue({k}); }} catch(e) {{ /* Property cannot be set: */ }}"); return
        for key in k:
            t=key["t"]/fps; v=key["s"]; v=v[0] if isinstance(v,list) and len(v)==1 else v
            # FIXED: Add error handling for keyframe properties that can't be set
            self.x.add(f"try {{ {path}.setValueAtTime({t},{v}); }} catch(e) {{ /* Property cannot be set: */ }}")
            if "i"in key and "o"in key:
                self.x.add(f"try {{ (function(){{var ki={path}.nearestKeyIndex({t});{path}.setTemporalEaseAtKey(ki,{key['i']},{key['o']});}})(); }} catch(e) {{ /* Easing cannot be set: */ }}")
    # effects
    def _effects(self,lj,lvar,fps):
        ep=lj.get("ef",[]); 
        if not ep: return
        self.x.add(f"var effPar={lvar}.property('ADBE Effect Parade');")
        for i,ef in enumerate(ep):
            ev=self.x.var(f"ef{i}")
            self.x.add(f"var {ev}=effPar.addProperty('{ef['mn']}'); {ev}.name='{ef.get('nm',ef['mn'])}';")
            for p in ef.get("ef",[]): self._efprop(p,ev,fps)
    def _efprop(self,p,ev,fps):
        path=f"{ev}.property('{p['mn']}')"
        if p.get("expen"):
            safe_exp = p['exp'].replace('\\','\\\\').replace("'","\\'")
            self.x.add(f"{path}.expression='{safe_exp}';"); return
        self._prop(path,p.get("v",{}),fps)
    # layer styles
    def _styles(self,lj,lvar,fps):
        sy=lj.get("sy",[]);
        if not sy: return

        # FIXED: Layer styles can only be applied to certain layer types
        # Skip layer styles for precomp layers (type 0) and other incompatible types
        layer_type = lj.get("ty", -1)
        if layer_type == 0:  # precomp layer - doesn't support layer styles
            return

        self.x.add(f"var ls={lvar}.property('ADBE Layer Styles');")
        for sty in sy:
            sv=self.x.var("sty")
            self.x.add(f"var {sv}=ls.addProperty('ADBE {sty['nm']}');")
            for k,v in sty.items():
                if k in ("nm","ty"): continue
                self._prop(f"{sv}.property('{k}')",v,fps)
    # masks
    def _masks(self,lj,lvar,fps):
        for m in lj.get("masksProperties",[]):
            mv=self.x.var("mask")
            self.x.add(f"var {mv}={lvar}.Masks.addProperty('Mask'); {mv}.name='{m.get('nm','Mask')}';")
            self._prop(f"{mv}.property('maskShape')",m["pt"],fps)
            md={0:'ADD',1:'SUBTRACT',2:'NONE',3:'INTERSECT',4:'LIGHTEN',5:'DARKEN',6:'DIFFERENCE'}
            self.x.add(f"{mv}.maskMode=MaskMode.{md.get(m.get('mode',0))};")
            if "o" in m: self._prop(f"{mv}.property('maskOpacity')",m["o"],fps)
    def _track(self,lj,lvar,cv):
        if "tt" in lj:
            mt={1:'ALPHA',2:'ALPHA_INVERTED',3:'LUMA',4:'LUMA_INVERTED'}
            self.x.add(f"{lvar}.trackMatteType=TrackMatteType.{mt[lj['tt']]};")
    # text animators (basic)
    def _text_anim(self,lj,lvar,fps):
        ta=lj.get("t",{}).get("a",[])
        for a in ta:
            av=self.x.var("ta")
            self.x.add(f"var {av}={lvar}.property('ADBE Text Animators').addProperty('ADBE Text Animator');")
            sa=a.get("s",{})
            for k,v in sa.items():
                self._prop(f"{av}.property('{k}')",v,fps)
            for sel in a.get("r",[]):
                sv=self.x.var("sel")
                self.x.add(f"var {sv}={av}.property('ADBE Text Selectors').addProperty('ADBE Text Selector');")
                self._prop(f"{sv}.property('ADBE Text Selector Range Start')",sel["s"],fps)
                self._prop(f"{sv}.property('ADBE Text Selector Range End')",sel["e"],fps)

    # shape layer walker (advanced)
    def _shape(self, lj, lvar, fps):
        """Full recursive walk of Bodymovin 'shapes' array → AE ShapeLayer."""
        def walk(items, parent_path):
            for it in items:
                ty = it["ty"]

                # -------- vector groups --------------------------------------
                if ty == "gr":
                    gvar = self.x.var("grp")
                    self.x.add(
                        f"var {gvar} = {parent_path}.addProperty('ADBE Vector Group'); "
                        f"{gvar}.name = '{it.get('nm','Group')}';")
                    walk(it.get("it", []), f"{gvar}.property('ADBE Vector Group')")
                    continue

                # map BM type → matchName
                match = {
                    "sh": "ADBE Vector Shape - Group",
                    "rc": "ADBE Vector Shape - Rect",
                    "el": "ADBE Vector Shape - Ellipse",
                    "pol": "ADBE Vector Shape - Star",   # polystar
                    "sr": "ADBE Vector Graphic - G-Fill",  # gradient stroke
                    "gs": "ADBE Vector Graphic - G-Fill",  # gradient fill
                    "fl": "ADBE Vector Graphic - Fill",
                    "st": "ADBE Vector Graphic - Stroke",
                    "gf": "ADBE Vector Graphic - G-Fill",
                    "tr": "ADBE Vector Transform Group",
                    "tm": "ADBE Vector Filter - Trim",
                    "rd": "ADBE Vector Filter - RC",
                    "rp": "ADBE Vector Filter - Repeater",
                    "op": "ADBE Vector Filter - Offset",
                    "zz": "ADBE Vector Filter - Zig Zag",
                    "ms": "ADBE Vector Filter - Merge",
                    "pb": "ADBE Vector Filter - Pucker & Bloat",
                }.get(ty)

                if not match:
                    continue  # unsupported; skip silently

                vvar = self.x.var(ty)
                self.x.add(f"var {vvar} = {parent_path}.addProperty('{match}');")

                # common params ------------------------------------------------
                if "nm" in it:
                    self.x.add(f"{vvar}.name = '{it['nm']}';")

                # shapes
                if ty in ("sh", "rc", "el", "pol"):
                    if "ks" in it:
                        self._prop(f"{vvar}.property('Path')", it["ks"], fps)
                    if "p" in it:   # size/position on rect/ellipse
                        self._prop(f"{vvar}.property('Size')", it["p"], fps)

                # stroke / fill
                if ty in ("st", "gs", "sr", "gf", "fl"):
                    if "c" in it:
                        self._prop(f"{vvar}.property('Color')", it["c"], fps)
                    if "o" in it:
                        self._prop(f"{vvar}.property('Opacity')", it["o"], fps)
                    if "w" in it:
                        self._prop(f"{vvar}.property('Stroke Width')", it["w"], fps)
                    if "lc" in it:  # line cap
                        self.x.add(f"{vvar}.property('ADBE Vector Stroke Line Cap').setValue({it['lc']});")
                    if "lj" in it:
                        self.x.add(f"{vvar}.property('ADBE Vector Stroke Line Join').setValue({it['lj']});")

                # gradient override
                if ty in ("gs", "gf", "sr"):
                    if "g" in it and "k" in it["g"]:
                        self._prop(f"{vvar}.property('ADBE Vector Grad Colors')", it["g"], fps)
                    if "s" in it:
                        self._prop(f"{vvar}.property('Start Point')", it["s"], fps)
                    if "e" in it:
                        self._prop(f"{vvar}.property('End Point')", it["e"], fps)
                    if "t" in it:
                        self.x.add(f"{vvar}.property('ADBE Vector Grad Type').setValue({1 if it['t'] else 2});")

                # transform group
                if ty == "tr":
                    mp = {"o":"Opacity","a":"Anchor Point","p":"Position",
                          "s":"Scale","r":"Rotation","sk":"Skew","sa":"Skew Axis"}
                    for k, pn in mp.items():
                        if k in it:
                            self._prop(f"{vvar}.property('{pn}')", it[k], fps)

                # filters  (trim, round, zigzag, repeater, etc.)
                if ty == "tm":
                    if "s" in it: self._prop(f"{vvar}.property('Start')", it["s"], fps)
                    if "e" in it: self._prop(f"{vvar}.property('End')", it["e"], fps)
                    if "o" in it: self._prop(f"{vvar}.property('Offset')", it["o"], fps)

                if ty == "rp":
                    for k in ("c","o","tr"):
                        if k in it:
                            tgt = {"c":"Copies","o":"Offset","tr":"ADBE Vector Transform Group"}[k]
                            if k == "tr":
                                mp={"p":"Position","s":"Scale","r":"Rotation","o":"Opacity"}
                                for kk, pn in mp.items():
                                    if kk in it["tr"]:
                                        self._prop(f"{vvar}.property('{tgt}').property('{pn}')",
                                                   it["tr"][kk], fps)
                            else:
                                self._prop(f"{vvar}.property('{tgt}')", it[k], fps)

                # recurse children if this item also has sub-items
                if "it" in it and ty != "gr":
                    walk(it["it"], parent_path)  # usually not needed, but safe

        walk(lj.get("shapes", []), f"{lvar}.property('Contents')")

# ────────────────────────────────────────────────────────────────────
def main():
    if len(sys.argv)!=3: sys.exit("use: python script.py in.json out.jsx")
    data=json.loads(pathlib.Path(sys.argv[1]).read_text())
    jsx=JSX(); Conv(data,jsx); jsx.write(sys.argv[2])
if __name__=="__main__": main()

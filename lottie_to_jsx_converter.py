#!/usr/bin/env python3
"""
SIMPLIFIED LOTTIE TO JSX CONVERTER (Based on working old version)
Converts Bodymovin/Lottie JSON files to After Effects JSX scripts
with JSON shim and basic error handling
"""

import json
import sys
import os

def convert_lottie_to_jsx(json_file_path, output_jsx_path):
    """Main conversion function"""
    print(f"🎬 Loading JSON file: {json_file_path}")
    
    with open(json_file_path, 'r', encoding='utf-8') as f:
        lottie_data = json.load(f)
    
    print("🔄 Converting to JSX...")
    jsx_script = generate_jsx_script(lottie_data)
    
    print(f"💾 Writing JSX file: {output_jsx_path}")
    with open(output_jsx_path, 'w', encoding='utf-8') as f:
        f.write(jsx_script)
    
    print("✅ Conversion complete!")
    return jsx_script

def generate_jsx_script(data):
    """Generate JSX script from Lottie data"""
    lines = []
    
    # Add JSON shim for After Effects
    lines.extend([
        "// J<PERSON><PERSON> shim for After Effects (ECMAScript 3)",
        "if (typeof JSON === 'undefined') {",
        "    JSON = {};",
        "    JSON.stringify = function(obj) {",
        "        if (obj === null) return 'null';",
        "        if (typeof obj === 'string') return '\"' + obj.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"') + '\"';",
        "        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);",
        "        if (obj instanceof Array) {",
        "            var arr = [];",
        "            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));",
        "            return '[' + arr.join(',') + ']';",
        "        }",
        "        if (typeof obj === 'object') {",
        "            var pairs = [];",
        "            for (var key in obj) {",
        "                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));",
        "            }",
        "            return '{' + pairs.join(',') + '}';",
        "        }",
        "        return 'undefined';",
        "    };",
        "}",
        "",
    ])
    
    # Add error handling wrapper
    lines.extend([
        "// Error handling wrapper",
        "try {",
        "",
    ])
    
    # Header
    name = data.get('nm', 'Converted Animation')
    version = data.get('v', '5.12.1')
    lines.extend([
        f"// {name} - Converted from Lottie JSON",
        f"// Original Bodymovin version: {version}",
        "app.beginUndoGroup('Create Animation');",
        "",
    ])
    
    # Helper functions
    lines.extend([
        "// Helper Functions",
        "function setKeyframes(property, keyframes, frameRate) {",
        "    if (!keyframes || !keyframes.length) return;",
        "    for (var i = 0; i < keyframes.length; i++) {",
        "        var kf = keyframes[i];",
        "        var time = kf.t / frameRate;",
        "        if (i === 0) {",
        "            property.setValueAtTime(time, kf.s);",
        "        } else {",
        "            property.setValueAtTime(time, kf.s);",
        "            if (kf.i && kf.o) {",
        "                var keyIndex = property.nearestKeyIndex(time);",
        "                try {",
        "                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);",
        "                } catch(e) {",
        "                    // Ignore easing errors",
        "                }",
        "            }",
        "        }",
        "    }",
        "}",
        "",
        "function createSolidLayer(comp, name, color, width, height) {",
        "    return comp.layers.addSolid(color, name, width, height, 1.0);",
        "}",
        "",
        "function createShapeLayer(comp, name) {",
        "    var layer = comp.layers.addShape();",
        "    layer.name = name;",
        "    return layer;",
        "}",
        "",
        "function createNullLayer(comp, name) {",
        "    var layer = comp.layers.addNull();",
        "    layer.name = name;",
        "    return layer;",
        "}",
        "",
        "function createTextLayer(comp, name) {",
        "    var layer = comp.layers.addText(name);",
        "    return layer;",
        "}",
        "",
    ])
    
    # Create main composition
    width = data.get('w', 1920)
    height = data.get('h', 1080)
    frame_rate = data.get('fr', 24)
    duration = (data.get('op', 120) - data.get('ip', 0)) / frame_rate
    if duration <= 0:
        duration = 5.0  # Default 5 seconds
    
    lines.extend([
        f"// Create main composition: {name}",
        f"var comp = app.project.items.addComp('{name}', {width}, {height}, 1.0, {duration:.3f}, {frame_rate});",
        f"var frameRate = {frame_rate};",
        "",
    ])
    
    # Process layers
    if 'layers' in data and data['layers']:
        lines.append(f"// Creating {len(data['layers'])} layers")
        for i, layer in enumerate(data['layers']):
            layer_jsx = create_layer(layer, i)
            lines.extend(layer_jsx)
    
    # Footer
    lines.extend([
        "",
        "// Set active composition",
        "app.project.activeItem = comp;",
        "app.endUndoGroup();",
        "",
        "} catch (e) {",
        "    app.endUndoGroup();",
        "    alert('Error in JSX script: ' + e.toString() + '\\nLine: ' + (e.line || 'unknown'));",
        "}",
    ])
    
    return '\n'.join(lines)

def create_layer(layer, index):
    """Create a single layer"""
    lines = []
    layer_type = layer.get('ty', 0)
    layer_name = layer.get('nm', f'Layer {index+1}').replace("'", "\\'")
    layer_var = f"layer_{index}"
    
    lines.append(f"// Layer {index+1}: {layer_name}")
    
    # Create layer based on type
    if layer_type == 1:  # Solid
        lines.extend(create_solid_layer(layer, layer_var))
    elif layer_type == 4:  # Shape
        lines.extend(create_shape_layer_simple(layer, layer_var))
    elif layer_type == 5:  # Text
        lines.extend(create_text_layer_simple(layer, layer_var))
    elif layer_type == 3:  # Null
        lines.extend(create_null_layer_simple(layer, layer_var))
    else:
        # Default to null layer for unknown types
        lines.extend(create_null_layer_simple(layer, layer_var))
    
    # Set basic transform properties
    lines.extend(set_transform_properties(layer, layer_var))
    
    lines.append("")
    return lines

def create_solid_layer(layer, layer_var):
    """Create solid layer"""
    lines = []
    layer_name = layer.get('nm', 'Solid').replace("'", "\\'")
    
    # Get solid color (default to white if not found)
    solid_color = layer.get('sc', '#ffffff')
    if solid_color.startswith('#') and len(solid_color) == 7:
        try:
            r = int(solid_color[1:3], 16) / 255.0
            g = int(solid_color[3:5], 16) / 255.0
            b = int(solid_color[5:7], 16) / 255.0
            color_array = f"[{r:.6f}, {g:.6f}, {b:.6f}]"
        except:
            color_array = "[1, 1, 1]"  # Default white
    else:
        color_array = "[1, 1, 1]"  # Default white
    
    width = layer.get('sw', 1920)
    height = layer.get('sh', 1080)
    
    lines.append(f"var {layer_var} = createSolidLayer(comp, '{layer_name}', {color_array}, {width}, {height});")
    return lines

def create_shape_layer_simple(layer, layer_var):
    """Create simple shape layer"""
    lines = []
    layer_name = layer.get('nm', 'Shape').replace("'", "\\'")
    lines.append(f"var {layer_var} = createShapeLayer(comp, '{layer_name}');")
    return lines

def create_text_layer_simple(layer, layer_var):
    """Create simple text layer"""
    lines = []
    layer_name = layer.get('nm', 'Text').replace("'", "\\'")
    lines.append(f"var {layer_var} = createTextLayer(comp, '{layer_name}');")
    return lines

def create_null_layer_simple(layer, layer_var):
    """Create null layer"""
    lines = []
    layer_name = layer.get('nm', 'Null').replace("'", "\\'")
    lines.append(f"var {layer_var} = createNullLayer(comp, '{layer_name}');")
    return lines

def set_transform_properties(layer, layer_var):
    """Set basic transform properties"""
    lines = []
    
    if 'ks' in layer:
        ks = layer['ks']
        
        # Position
        if 'p' in ks and 'k' in ks['p']:
            pos = ks['p']['k']
            if isinstance(pos, list) and len(pos) >= 2:
                lines.append(f"{layer_var}.property('Transform').property('Position').setValue([{pos[0]}, {pos[1]}]);")
        
        # Scale
        if 's' in ks and 'k' in ks['s']:
            scale = ks['s']['k']
            if isinstance(scale, list) and len(scale) >= 2:
                lines.append(f"{layer_var}.property('Transform').property('Scale').setValue([{scale[0]}, {scale[1]}]);")
        
        # Rotation
        if 'r' in ks and 'k' in ks['r']:
            rotation = ks['r']['k']
            if isinstance(rotation, (int, float)):
                lines.append(f"{layer_var}.property('Transform').property('Rotation').setValue({rotation});")
        
        # Opacity
        if 'o' in ks and 'k' in ks['o']:
            opacity = ks['o']['k']
            if isinstance(opacity, (int, float)):
                lines.append(f"{layer_var}.property('Transform').property('Opacity').setValue({opacity});")
        
        # Anchor Point
        if 'a' in ks and 'k' in ks['a']:
            anchor = ks['a']['k']
            if isinstance(anchor, list) and len(anchor) >= 2:
                lines.append(f"{layer_var}.property('Transform').property('Anchor Point').setValue([{anchor[0]}, {anchor[1]}]);")
    
    return lines

def main():
    """Main function"""
    if len(sys.argv) != 3:
        print("Usage: python lottie_to_jsx_converter_simple.py input.json output.jsx")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found")
        sys.exit(1)
    
    try:
        convert_lottie_to_jsx(input_file, output_file)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()

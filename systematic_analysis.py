#!/usr/bin/env python3
"""
SYSTEMATIC ANALYSIS of Lottie JSON for proper layer ordering
This will analyze the ACTUAL structure and parent-child relationships
"""

import json
from typing import Dict, List, Any

def analyze_lottie_structure():
    """Perform systematic analysis of the Lottie JSON structure"""
    
    print("=== SYSTEMATIC LOTTIE ANALYSIS ===")
    
    with open('cat.json', 'r') as f:
        data = json.load(f)
    
    layers = data.get('layers', [])
    print(f"Total layers: {len(layers)}")
    
    # 1. EXTRACT LAYER METADATA
    print("\n1. LAYER METADATA:")
    layer_info = {}
    for layer in layers:
        ind = layer.get('ind', 0)
        name = layer.get('nm', '')
        parent = layer.get('parent', None)
        layer_type = layer.get('ty', 0)
        
        layer_info[ind] = {
            'name': name,
            'parent': parent,
            'type': layer_type,
            'children': []
        }
        
        print(f"  Layer {ind:2d}: '{name}' (type: {layer_type}, parent: {parent})")
    
    # 2. BUILD HIERARCHY TREE
    print("\n2. PARENT-CHILD RELATIONSHIPS:")
    for ind, info in layer_info.items():
        if info['parent'] and info['parent'] in layer_info:
            layer_info[info['parent']]['children'].append(ind)
    
    def print_hierarchy(ind, level=0):
        if ind not in layer_info:
            return
        info = layer_info[ind]
        indent = "  " * level
        print(f"{indent}├─ {ind}: {info['name']}")
        for child in sorted(info['children']):
            print_hierarchy(child, level + 1)
    
    # Find root layers (no parent)
    roots = [ind for ind, info in layer_info.items() if not info['parent']]
    print("HIERARCHY TREE:")
    for root in sorted(roots):
        print_hierarchy(root)
    
    # 3. ANALYZE VISUAL DEPTH REQUIREMENTS
    print("\n3. VISUAL DEPTH ANALYSIS:")
    print("Based on cat anatomy, the LOGICAL depth order should be:")
    print("  BACK → FRONT:")
    print("  1. Back legs (furthest from viewer)")
    print("  2. Body/torso (middle)")
    print("  3. Tail (attached to body)")
    print("  4. Front legs (closer to viewer)")
    print("  5. Head/face (front)")
    print("  6. Facial features (frontmost)")
    
    # 4. CATEGORIZE LAYERS BY ANATOMY
    print("\n4. ANATOMICAL CATEGORIZATION:")
    
    categories = {
        'back_legs': [],
        'body_core': [],
        'front_legs': [],
        'head_face': [],
        'facial_features': []
    }
    
    for ind, info in layer_info.items():
        name = info['name'].lower()
        
        # Analyze each layer name systematically
        if 'thigh' in name and ('b' in name or 'back' in name):
            categories['back_legs'].append((ind, info['name'], 'back_thigh'))
        elif 'leg' in name and ('b' in name or 'back' in name):
            categories['back_legs'].append((ind, info['name'], 'back_leg'))
        elif 'l thigh' in name and 'f' not in name:  # "L Thigh" without F
            categories['back_legs'].append((ind, info['name'], 'back_left_thigh'))
        elif 'l leg' in name and 'f' not in name:    # "L Leg" without F
            categories['back_legs'].append((ind, info['name'], 'back_left_leg'))
        elif 'thigh' in name and 'f' in name:
            categories['front_legs'].append((ind, info['name'], 'front_thigh'))
        elif 'leg' in name and 'f' in name:
            categories['front_legs'].append((ind, info['name'], 'front_leg'))
        elif 'r leg' in name and 'b' not in name:   # "R Leg" without B
            categories['front_legs'].append((ind, info['name'], 'front_right_leg'))
        elif name == 'body':
            categories['body_core'].append((ind, info['name'], 'main_body'))
        elif name == 'tail':
            categories['body_core'].append((ind, info['name'], 'tail'))
        elif name == 'face':
            categories['head_face'].append((ind, info['name'], 'face'))
        elif 'eye' in name or 'mouth' in name:
            categories['facial_features'].append((ind, info['name'], 'feature'))
        else:
            print(f"  UNCLASSIFIED: {ind} - '{info['name']}'")
    
    # Print categorization
    for category, items in categories.items():
        if items:
            print(f"\n  {category.upper()}:")
            for ind, name, subtype in items:
                parent_info = f" (parent: {layer_info[ind]['parent']})" if layer_info[ind]['parent'] else ""
                print(f"    {ind:2d}: {name:15s} [{subtype}]{parent_info}")
    
    # 5. DETERMINE OPTIMAL RENDER ORDER
    print("\n5. OPTIMAL RENDER ORDER (back to front):")
    print("   After Effects creates layers in REVERSE order:")
    print("   - First created = highest layer number = back")
    print("   - Last created = lowest layer number = front")
    
    optimal_order = []
    
    # Back legs first (furthest back)
    back_legs = sorted(categories['back_legs'], key=lambda x: x[0])
    optimal_order.extend(back_legs)
    
    # Body core
    body_core = sorted(categories['body_core'], key=lambda x: x[0])
    optimal_order.extend(body_core)
    
    # Front legs
    front_legs = sorted(categories['front_legs'], key=lambda x: x[0])
    optimal_order.extend(front_legs)
    
    # Head/face
    head_face = sorted(categories['head_face'], key=lambda x: x[0])
    optimal_order.extend(head_face)
    
    # Facial features (frontmost)
    facial_features = sorted(categories['facial_features'], key=lambda x: x[0])
    optimal_order.extend(facial_features)
    
    print("\n   CORRECT ORDER:")
    for i, (ind, name, subtype) in enumerate(optimal_order, 1):
        print(f"   {i:2d}. Layer {ind:2d}: {name:15s} [{subtype}]")
    
    # 6. GENERATE PRIORITY MAPPING
    print("\n6. PRIORITY MAPPING FOR CONVERTER:")
    priority_map = {}
    
    for i, (ind, name, subtype) in enumerate(optimal_order):
        priority = (i * 5)  # Space out priorities
        priority_map[ind] = priority
        print(f"   Layer {ind:2d} ('{name}'): priority {priority}")
    
    return priority_map, layer_info

if __name__ == "__main__":
    priority_map, layer_info = analyze_lottie_structure()

// <PERSON><PERSON><PERSON> shim for After Effects (ECMAScript 3)
if (typeof JSON === 'undefined') {
    JSON = {};
    JSON.stringify = function(obj) {
        if (obj === null) return 'null';
        if (typeof obj === 'string') return '"' + obj.replace(/\\/g, '\\\\').replace(/"/g, '\\"') + '"';
        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
        if (obj instanceof Array) {
            var arr = [];
            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));
            return '[' + arr.join(',') + ']';
        }
        if (typeof obj === 'object') {
            var pairs = [];
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));
            }
            return '{' + pairs.join(',') + '}';
        }
        return 'undefined';
    };
}

// Error handling wrapper
try {

// Test Composition - Converted from Lottie JSON
// Original Bodymovin version: 5.7.4
app.beginUndoGroup('Create Animation');

// Helper Functions
function setKeyframes(property, keyframes, frameRate) {
    if (!keyframes || !keyframes.length) return;
    for (var i = 0; i < keyframes.length; i++) {
        var kf = keyframes[i];
        var time = kf.t / frameRate;
        if (i === 0) {
            property.setValueAtTime(time, kf.s);
        } else {
            property.setValueAtTime(time, kf.s);
            if (kf.i && kf.o) {
                var keyIndex = property.nearestKeyIndex(time);
                try {
                    property.setTemporalEaseAtKey(keyIndex, kf.i, kf.o);
                } catch(e) {
                    // Ignore easing errors
                }
            }
        }
    }
}

function createSolidLayer(comp, name, color, width, height) {
    return comp.layers.addSolid(color, name, width, height, 1.0);
}

function createShapeLayer(comp, name) {
    var layer = comp.layers.addShape();
    layer.name = name;
    return layer;
}

function createNullLayer(comp, name) {
    var layer = comp.layers.addNull();
    layer.name = name;
    return layer;
}

function createTextLayer(comp, name) {
    var layer = comp.layers.addText(name);
    return layer;
}

// Create main composition: Test Composition
var comp = app.project.items.addComp('Test Composition', 1920, 1080, 1.0, 2.000, 30);
var frameRate = 30;

// Creating 1 layers
// Layer 1: Test Solid
var layer_0 = createSolidLayer(comp, 'Test Solid', [1.000000, 0.000000, 0.000000], 100, 100);
layer_0.property('Transform').property('Position').setValue([960, 540]);
layer_0.property('Transform').property('Scale').setValue([100, 100]);
layer_0.property('Transform').property('Rotation').setValue(0);
layer_0.property('Transform').property('Opacity').setValue(100);
layer_0.property('Transform').property('Anchor Point').setValue([50, 50]);


// Set active composition
app.project.activeItem = comp;
app.endUndoGroup();

} catch (e) {
    app.endUndoGroup();
    alert('Error in JSX script: ' + e.toString() + '\nLine: ' + (e.line || 'unknown'));
}
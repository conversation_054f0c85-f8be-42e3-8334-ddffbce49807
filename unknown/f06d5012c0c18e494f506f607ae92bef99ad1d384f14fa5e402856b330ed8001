// <PERSON><PERSON><PERSON> shim for After Effects
if (typeof JSON === 'undefined') {
    JSON = {};
    JSON.stringify = function(obj) {
        if (obj === null) return 'null';
        if (typeof obj === 'string') return '"' + obj.replace(/\\/g, '\\\\').replace(/"/g, '\\"') + '"';
        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
        if (obj instanceof Array) {
            var arr = [];
            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));
            return '[' + arr.join(',') + ']';
        }
        if (typeof obj === 'object') {
            var pairs = [];
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));
            }
            return '{' + pairs.join(',') + '}';
        }
        return 'undefined';
    };
}

// Error handling wrapper
try {

// universal rebuild
app.beginUndoGroup('Lottie→JSX');
var proj = app.project || app.newProject();

var comp_root=proj.items.addComp('Test Composition',1920,1080,1,2.0,30);
var L1=comp_root.layers.addSolid([1.0,0.0,0.0],'Test Solid',100,100,1);
L1.property('Transform').property('Position').setValue([960,540]);
L1.property('Transform').property('Scale').setValue([100,100]);
L1.property('Transform').property('Rotation').setValue(0);
L1.property('Transform').property('Opacity').setValue(100);
L1.property('Transform').property('Anchor Point').setValue([50,50]);
proj.activeItem=comp_root;

app.endUndoGroup();

} catch (e) {
    app.endUndoGroup();
    alert('Error in JSX script: ' + e.toString() + '\nLine: ' + e.line);
}
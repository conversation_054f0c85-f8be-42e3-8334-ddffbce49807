// <PERSON><PERSON><PERSON> shim for After Effects
if (typeof JSON === 'undefined') {
    JSON = {};
    JSON.stringify = function(obj) {
        if (obj === null) return 'null';
        if (typeof obj === 'string') return '"' + obj.replace(/\\/g, '\\\\').replace(/"/g, '\\"') + '"';
        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
        if (obj instanceof Array) {
            var arr = [];
            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));
            return '[' + arr.join(',') + ']';
        }
        if (typeof obj === 'object') {
            var pairs = [];
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));
            }
            return '{' + pairs.join(',') + '}';
        }
        return 'undefined';
    };
}

// universal rebuild
app.beginUndoGroup('Lottie→JSX');
var proj = app.project || app.newProject();

var comp_comp_0=proj.items.addComp('Top',1920,1080,1,0.03333333333333333,30);
var comp_comp_1=proj.items.addComp('Text',1920,1080,1,0.03333333333333333,30);
var L1_2=comp_comp_1.layers.addText('3D TEXT');
var ta=L1_2.property('ADBE Text Animators').addProperty('ADBE Text Animator');
ta.property('t').setValue(0);
ta.property('xe').setValue(0);
ta.property('ne').setValue(0);
ta.property('a').setValue(100);
ta.property('b').setValue(1);
ta.property('rn').setValue(0);
ta.property('sh').setValue(1);
ta.property('sm').setValue(100);
ta.property('r').setValue(1);
L1_2.property('Transform').property('Position').setValue([960, 540, 0]);
L1_2.property('Transform').property('Scale').setValue([139, 139, 100]);
L1_2.property('Transform').property('Rotation').setValue(0);
L1_2.property('Transform').property('Opacity').setValue(100);
L1_2.property('Transform').property('Anchor Point').setValue([-13.672, -109.375, 0]);

var L1=comp_comp_0.layers.add(comp_comp_1);
L1.property('Transform').property('Position').setValue([960, 540, 0]);
L1.property('Transform').property('Scale').setValue([100, 100, 100]);
L1.property('Transform').property('Rotation').setValue(0);
L1.property('Transform').property('Opacity').setValue(100);
L1.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L1.property('ADBE Effect Parade');
var ef0=effPar.addProperty('ADBE Fill'); ef0.name='Fill';
ef0.property('ADBE Fill-0001').setValue(0);
ef0.property('ADBE Fill-0007').setValue(0);
ef0.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
ef0.property('ADBE Fill-0006').setValue(0);
ef0.property('ADBE Fill-0003').setValue(0);
ef0.property('ADBE Fill-0004').setValue(0);
ef0.property('ADBE Fill-0005').setValue(1);
var ls=L1.property('ADBE Layer Styles');
var sty=ls.addProperty('ADBE Inner Shadow');
sty.property('c').setValue([0.549450218678, 0.690196096897, 0.338331401348, 1]);
sty.property('o').setValue(49.5);
sty.property('a').setValue(30);
sty.property('s').setValue(0);
sty.property('d').setValue(10.5);
sty.property('ch').setValue(0);
sty.property('bm').setValue(5);
sty.property('no').setValue(0);
var L2=comp_comp_0.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 2',1920,1080,1);
L2.property('Transform').property('Position').setValue([960, 540, 0]);
L2.property('Transform').property('Scale').setValue([100, 100, 100]);
L2.property('Transform').property('Rotation').setValue(10.3);
L2.property('Transform').property('Opacity').setValue(100);
L2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
L2.trackMatteType=TrackMatteType.ALPHA;
var effPar=L2.property('ADBE Effect Parade');
var ef0_2=effPar.addProperty('ADBE Grid'); ef0_2.name='Grid';
ef0_2.property('ADBE Grid-0001').setValue([960, 540]);
ef0_2.property('ADBE Grid-0002').setValue(2);
ef0_2.property('ADBE Grid-0003').setValue([1152, 648]);
ef0_2.property('ADBE Grid-0004').setValue(25);
ef0_2.property('ADBE Grid-0005').setValue(24);
ef0_2.property('ADBE Grid-0006').setValue(1.5);
ef0_2.property('ADBE Grid-0007').setValue(0);
ef0_2.property('ADBE Grid-0008').setValue(0);
ef0_2.property('ADBE Grid-0009').setValue(0);
ef0_2.property('ADBE Grid-0010').setValue(0);
ef0_2.property('ADBE Grid-0011').setValue(0);
ef0_2.property('ADBE Grid-0012').setValue([0, 0, 0, 1]);
ef0_2.property('ADBE Grid-0013').setValue(100);
ef0_2.property('ADBE Grid-0014').setValue(1);
var L3=comp_comp_0.layers.add(comp_comp_1);
L3.property('Transform').property('Position').setValue([960, 540, 0]);
L3.property('Transform').property('Scale').setValue([100, 100, 100]);
L3.property('Transform').property('Rotation').setValue(0);
L3.property('Transform').property('Opacity').setValue(100);
L3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L3.property('ADBE Effect Parade');
var ef0_3=effPar.addProperty('ADBE Fill'); ef0_3.name='Fill';
ef0_3.property('ADBE Fill-0001').setValue(0);
ef0_3.property('ADBE Fill-0007').setValue(0);
ef0_3.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
ef0_3.property('ADBE Fill-0006').setValue(0);
ef0_3.property('ADBE Fill-0003').setValue(0);
ef0_3.property('ADBE Fill-0004').setValue(0);
ef0_3.property('ADBE Fill-0005').setValue(1);
var ls=L3.property('ADBE Layer Styles');
var sty_2=ls.addProperty('ADBE Inner Shadow');
sty_2.property('c').setValue([0.549450218678, 0.690196096897, 0.338331401348, 1]);
sty_2.property('o').setValue(49.5);
sty_2.property('a').setValue(30);
sty_2.property('s').setValue(0);
sty_2.property('d').setValue(10.5);
sty_2.property('ch').setValue(0);
sty_2.property('bm').setValue(5);
sty_2.property('no').setValue(0);
var L4=comp_comp_0.layers.add(comp_comp_1);
L4.property('Transform').property('Position').setValue([960, 540, 0]);
L4.property('Transform').property('Scale').setValue([100, 100, 100]);
L4.property('Transform').property('Rotation').setValue(0);
L4.property('Transform').property('Opacity').setValue(100);
L4.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L4.property('ADBE Effect Parade');
var ef0_4=effPar.addProperty('ADBE Fill'); ef0_4.name='Fill';
ef0_4.property('ADBE Fill-0001').setValue(0);
ef0_4.property('ADBE Fill-0007').setValue(0);
ef0_4.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
ef0_4.property('ADBE Fill-0006').setValue(0);
ef0_4.property('ADBE Fill-0003').setValue(0);
ef0_4.property('ADBE Fill-0004').setValue(0);
ef0_4.property('ADBE Fill-0005').setValue(1);
var ls=L4.property('ADBE Layer Styles');
var sty_3=ls.addProperty('ADBE Stroke');
sty_3.property('c').setValue([0, 0, 0, 1]);
sty_3.property('s').setValue(3);
var L5=comp_comp_0.layers.add(comp_comp_1);
L5.property('Transform').property('Position').setValue([960, 540, 0]);
L5.property('Transform').property('Scale').setValue([100, 100, 100]);
L5.property('Transform').property('Rotation').setValue(0);
L5.property('Transform').property('Opacity').setValue(100);
L5.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L5.property('ADBE Effect Parade');
var ef0_5=effPar.addProperty('ADBE Fill'); ef0_5.name='Fill';
ef0_5.property('ADBE Fill-0001').setValue(0);
ef0_5.property('ADBE Fill-0007').setValue(0);
ef0_5.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
ef0_5.property('ADBE Fill-0006').setValue(0);
ef0_5.property('ADBE Fill-0003').setValue(0);
ef0_5.property('ADBE Fill-0004').setValue(0);
ef0_5.property('ADBE Fill-0005').setValue(1);
var ls=L5.property('ADBE Layer Styles');
var sty_4=ls.addProperty('ADBE Stroke');
sty_4.property('c').setValue([0.749019622803, 0.937254965305, 0.466666698456, 1]);
sty_4.property('s').setValue(9.5);
var L6=comp_comp_0.layers.add(comp_comp_1);
L6.property('Transform').property('Position').setValue([960, 540, 0]);
L6.property('Transform').property('Scale').setValue([100, 100, 100]);
L6.property('Transform').property('Rotation').setValue(0);
L6.property('Transform').property('Opacity').setValue(100);
L6.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L6.property('ADBE Effect Parade');
var ef0_6=effPar.addProperty('ADBE Fill'); ef0_6.name='Fill';
ef0_6.property('ADBE Fill-0001').setValue(0);
ef0_6.property('ADBE Fill-0007').setValue(0);
ef0_6.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]);
ef0_6.property('ADBE Fill-0006').setValue(0);
ef0_6.property('ADBE Fill-0003').setValue(0);
ef0_6.property('ADBE Fill-0004').setValue(0);
ef0_6.property('ADBE Fill-0005').setValue(1);
var ls=L6.property('ADBE Layer Styles');
var sty_5=ls.addProperty('ADBE Stroke');
sty_5.property('c').setValue([0, 0, 0, 1]);
sty_5.property('s').setValue(12.77);

var comp_comp_2=proj.items.addComp('Mid',1920,1080,1,0.03333333333333333,30);
var L1_3=comp_comp_2.layers.add(comp_comp_1);
L1_3.property('Transform').property('Position').setValue([960, 540, 0]);
L1_3.property('Transform').property('Scale').setValue([100, 100, 100]);
L1_3.property('Transform').property('Rotation').setValue(0);
L1_3.property('Transform').property('Opacity').setValue(100);
L1_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L1_3.property('ADBE Effect Parade');
var ef0_7=effPar.addProperty('ADBE Fill'); ef0_7.name='Fill';
ef0_7.property('ADBE Fill-0001').setValue(0);
ef0_7.property('ADBE Fill-0007').setValue(0);
ef0_7.property('ADBE Fill-0002').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
ef0_7.property('ADBE Fill-0006').setValue(0);
ef0_7.property('ADBE Fill-0003').setValue(0);
ef0_7.property('ADBE Fill-0004').setValue(0);
ef0_7.property('ADBE Fill-0005').setValue(1);
var ef1=effPar.addProperty('CC Radial Fast Blur'); ef1.name='CC Radial Fast Blur';
ef1.property('CC Radial Fast Blur-0001').setValue([1836, 4]);
ef1.property('CC Radial Fast Blur-0002').setValue(12.5);
ef1.property('CC Radial Fast Blur-0003').setValue(1);
var ls=L1_3.property('ADBE Layer Styles');
var sty_6=ls.addProperty('ADBE Stroke');
sty_6.property('c').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
sty_6.property('s').setValue(12.77);
var L2_2=comp_comp_2.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 3',1920,1080,1);
L2_2.property('Transform').property('Position').setValue([960, 540, 0]);
L2_2.property('Transform').property('Scale').setValue([100, 100, 100]);
L2_2.property('Transform').property('Rotation').setValue(0);
L2_2.property('Transform').property('Opacity').setValue(100);
L2_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
L2_2.trackMatteType=TrackMatteType.ALPHA;
var effPar=L2_2.property('ADBE Effect Parade');
var ef0_8=effPar.addProperty('ADBE Grid'); ef0_8.name='Grid';
ef0_8.property('ADBE Grid-0001').setValue([1798, 540]);
ef0_8.property('ADBE Grid-0002').setValue(3);
ef0_8.property('ADBE Grid-0003').setValue([1152, 648]);
ef0_8.property('ADBE Grid-0004').setValue(1581);
ef0_8.property('ADBE Grid-0005').setValue(18.4);
ef0_8.property('ADBE Grid-0006').setValue(2.3);
ef0_8.property('ADBE Grid-0007').setValue(0);
ef0_8.property('ADBE Grid-0008').setValue(0);
ef0_8.property('ADBE Grid-0009').setValue(0);
ef0_8.property('ADBE Grid-0010').setValue(0);
ef0_8.property('ADBE Grid-0011').setValue(0);
ef0_8.property('ADBE Grid-0012').setValue([0, 0, 0, 1]);
ef0_8.property('ADBE Grid-0013').setValue(100);
ef0_8.property('ADBE Grid-0014').setValue(1);
var ef1_2=effPar.addProperty('ADBE Turbulent Displace'); ef1_2.name='Turbulent Displace';
ef1_2.property('ADBE Turbulent Displace-0001').setValue(1);
ef1_2.property('ADBE Turbulent Displace-0002').setValue(85);
ef1_2.property('ADBE Turbulent Displace-0003').setValue(153);
ef1_2.property('ADBE Turbulent Displace-0004').setValue([960, 540]);
ef1_2.property('ADBE Turbulent Displace-0005').setValue(1);
ef1_2.property('ADBE Turbulent Displace-0006').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0007').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0008').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0009').setValue(1);
ef1_2.property('ADBE Turbulent Displace-0010').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0011').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0012').setValue(3);
ef1_2.property('ADBE Turbulent Displace-0013').setValue(0);
ef1_2.property('ADBE Turbulent Displace-0014').setValue(1);
var L3_2=comp_comp_2.layers.add(comp_comp_1);
L3_2.property('Transform').property('Position').setValue([960, 540, 0]);
L3_2.property('Transform').property('Scale').setValue([100, 100, 100]);
L3_2.property('Transform').property('Rotation').setValue(0);
L3_2.property('Transform').property('Opacity').setValue(100);
L3_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L3_2.property('ADBE Effect Parade');
var ef0_9=effPar.addProperty('ADBE Fill'); ef0_9.name='Fill';
ef0_9.property('ADBE Fill-0001').setValue(0);
ef0_9.property('ADBE Fill-0007').setValue(0);
ef0_9.property('ADBE Fill-0002').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
ef0_9.property('ADBE Fill-0006').setValue(0);
ef0_9.property('ADBE Fill-0003').setValue(0);
ef0_9.property('ADBE Fill-0004').setValue(0);
ef0_9.property('ADBE Fill-0005').setValue(1);
var ef1_3=effPar.addProperty('CC Radial Fast Blur'); ef1_3.name='CC Radial Fast Blur';
ef1_3.property('CC Radial Fast Blur-0001').setValue([1836, 4]);
ef1_3.property('CC Radial Fast Blur-0002').setValue(12.5);
ef1_3.property('CC Radial Fast Blur-0003').setValue(1);
var ls=L3_2.property('ADBE Layer Styles');
var sty_7=ls.addProperty('ADBE Stroke');
sty_7.property('c').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]);
sty_7.property('s').setValue(12.77);

var comp_comp_3=proj.items.addComp('Bottom',1920,1080,1,0.03333333333333333,30);
var L1_4=comp_comp_3.layers.add(comp_comp_1);
L1_4.property('Transform').property('Position').setValue([960, 540, 0]);
L1_4.property('Transform').property('Scale').setValue([100, 100, 100]);
L1_4.property('Transform').property('Rotation').setValue(0);
L1_4.property('Transform').property('Opacity').setValue(100);
L1_4.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L1_4.property('ADBE Effect Parade');
var ef0_10=effPar.addProperty('ADBE Fill'); ef0_10.name='Fill';
ef0_10.property('ADBE Fill-0001').setValue(0);
ef0_10.property('ADBE Fill-0007').setValue(0);
ef0_10.property('ADBE Fill-0002').setValue([0.980377197266, 0.494110107422, 0.101959228516, 1]);
ef0_10.property('ADBE Fill-0006').setValue(0);
ef0_10.property('ADBE Fill-0003').setValue(0);
ef0_10.property('ADBE Fill-0004').setValue(0);
ef0_10.property('ADBE Fill-0005').setValue(1);
var ef1_4=effPar.addProperty('CC Radial Fast Blur'); ef1_4.name='CC Radial Fast Blur';
ef1_4.property('CC Radial Fast Blur-0001').setValue([1836, 4]);
ef1_4.property('CC Radial Fast Blur-0002').setValue(39.5);
ef1_4.property('CC Radial Fast Blur-0003').setValue(1);
var ls=L1_4.property('ADBE Layer Styles');
var sty_8=ls.addProperty('ADBE Stroke');
sty_8.property('c').setValue([0.980377197266, 0.494110107422, 0.101959228516, 1]);
sty_8.property('s').setValue(12.77);

var comp_root=proj.items.addComp('Main',1920,1080,1,10.0,30);
var L1_5=comp_root.layers.add(comp_comp_0);
L1_5.property('Transform').property('Position').setValue([960, 540, 0]);
L1_5.property('Transform').property('Scale').setValue([100, 100, 100]);
L1_5.property('Transform').property('Rotation').setValue(0);
L1_5.property('Transform').property('Opacity').setValue(100);
L1_5.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var L2_3=comp_root.layers.add(comp_comp_2);
L2_3.property('Transform').property('Position').setValue([960, 540, 0]);
L2_3.property('Transform').property('Scale').setValue([100, 100, 100]);
L2_3.property('Transform').property('Rotation').setValue(0);
L2_3.property('Transform').property('Opacity').setValue(100);
L2_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var ls=L2_3.property('ADBE Layer Styles');
var sty_9=ls.addProperty('ADBE Stroke');
sty_9.property('c').setValue([0, 0, 0, 1]);
sty_9.property('s').setValue(3.64);
var L3_3=comp_root.layers.add(comp_comp_3);
L3_3.property('Transform').property('Position').setValue([960, 540, 0]);
L3_3.property('Transform').property('Scale').setValue([100, 100, 100]);
L3_3.property('Transform').property('Rotation').setValue(0);
L3_3.property('Transform').property('Opacity').setValue(100);
L3_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var ls=L3_3.property('ADBE Layer Styles');
var sty_10=ls.addProperty('ADBE Stroke');
sty_10.property('c').setValue([0, 0, 0, 1]);
sty_10.property('s').setValue(3.6);
var L4_2=comp_root.layers.add(comp_comp_3);
L4_2.property('Transform').property('Position').setValue([960, 540, 0]);
L4_2.property('Transform').property('Scale').setValue([100, 100, 100]);
L4_2.property('Transform').property('Rotation').setValue(0);
L4_2.property('Transform').property('Opacity').setValue(100);
L4_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L4_2.property('ADBE Effect Parade');
var ef0_11=effPar.addProperty('ADBE Drop Shadow'); ef0_11.name='Drop Shadow';
ef0_11.property('ADBE Drop Shadow-0001').setValue([0, 0, 0, 1]);
ef0_11.property('ADBE Drop Shadow-0002').setValue(160.2);
ef0_11.property('ADBE Drop Shadow-0003').setValue(225);
ef0_11.property('ADBE Drop Shadow-0004').setValue(36.4);
ef0_11.property('ADBE Drop Shadow-0005').setValue(59);
ef0_11.property('ADBE Drop Shadow-0006').setValue(0);
var L5_2=comp_root.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 1',1920,1080,1);
L5_2.property('Transform').property('Position').setValue([960, 540, 0]);
L5_2.property('Transform').property('Scale').setValue([100, 100, 100]);
L5_2.property('Transform').property('Rotation').setValue(0);
L5_2.property('Transform').property('Opacity').setValue(100);
L5_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]);
var effPar=L5_2.property('ADBE Effect Parade');
var ef0_12=effPar.addProperty('ADBE Fill'); ef0_12.name='Fill';
ef0_12.property('ADBE Fill-0001').setValue(0);
ef0_12.property('ADBE Fill-0007').setValue(0);
ef0_12.property('ADBE Fill-0002').setValue([0.882354736328, 0.301971435547, 0.168640136719, 1]);
ef0_12.property('ADBE Fill-0006').setValue(0);
ef0_12.property('ADBE Fill-0003').setValue(0);
ef0_12.property('ADBE Fill-0004').setValue(0);
ef0_12.property('ADBE Fill-0005').setValue(1);
proj.activeItem=comp_root;

app.endUndoGroup();
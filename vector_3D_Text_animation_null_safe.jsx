// <PERSON><PERSON><PERSON> shim for After Effects ExtendScript
if (typeof JSON === 'undefined') {
    JSON = {};
    JSON.stringify = function(obj) {
        if (obj === null) return 'null';
        if (typeof obj === 'string') return '"' + obj.replace(/\\/g, '\\\\').replace(/"/g, '\\"') + '"';
        if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);
        if (obj instanceof Array) {
            var arr = [];
            for (var i = 0; i < obj.length; i++) arr.push(JSON.stringify(obj[i]));
            return '[' + arr.join(',') + ']';
        }
        if (typeof obj === 'object') {
            var pairs = [];
            for (var key in obj) {
                if (obj.hasOwnProperty(key)) pairs.push(JSON.stringify(key) + ':' + JSON.stringify(obj[key]));
            }
            return '{' + pairs.join(',') + '}';
        }
        return 'undefined';
    };
}

// auto-generated full rebuild
app.beginUndoGroup('JSON-to-JSX FULL');
var proj=app.project||app.newProject();

var comp_comp_0=proj.items.addComp('Top',1920,1080,1,0.033333,30);

var comp_comp_1=proj.items.addComp('Text',1920,1080,1,0.033333,30);

var comp_comp_2=proj.items.addComp('Mid',1920,1080,1,0.033333,30);

var comp_comp_3=proj.items.addComp('Bottom',1920,1080,1,0.033333,30);

var comp_root_2161978942400=proj.items.addComp('Main',1920,1080,1,10.000000,30);
comp_root_2161978942400.openInViewer();

var L1=comp_comp_0.layers.add(comp_comp_1);
try { L1.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L1.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L1.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L1.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L1.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L1.property('ADBE Effect Parade');
var ef0=effPar.addProperty('ADBE Fill'); ef0.name='Fill';
try { ef0.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L2=comp_comp_0.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 2',1920,1080,1);
try { L2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L2.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L2.property('Transform').property('Rotation').setValue(10.3); } catch(e) { /* Property cannot be set: */ }
try { L2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L2.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
L2.trackMatteType=TrackMatteType.ALPHA;
var effPar=L2.property('ADBE Effect Parade');
var ef0_2=effPar.addProperty('ADBE Grid'); ef0_2.name='Grid';
try { ef0_2.property('ADBE Grid-0001').setValue([960, 540]); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0002').setValue(2); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0003').setValue([1152, 648]); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0004').setValue(25); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0005').setValue(24); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0006').setValue(1.5); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0008').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0009').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0010').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0011').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0012').setValue([0, 0, 0, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0013').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { ef0_2.property('ADBE Grid-0014').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L3=comp_comp_0.layers.add(comp_comp_1);
try { L3.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L3.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L3.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L3.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L3.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L3.property('ADBE Effect Parade');
var ef0_3=effPar.addProperty('ADBE Fill'); ef0_3.name='Fill';
try { ef0_3.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_3.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L4=comp_comp_0.layers.add(comp_comp_1);
try { L4.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L4.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L4.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L4.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L4.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L4.property('ADBE Effect Parade');
var ef0_4=effPar.addProperty('ADBE Fill'); ef0_4.name='Fill';
try { ef0_4.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_4.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L5=comp_comp_0.layers.add(comp_comp_1);
try { L5.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L5.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L5.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L5.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L5.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L5.property('ADBE Effect Parade');
var ef0_5=effPar.addProperty('ADBE Fill'); ef0_5.name='Fill';
try { ef0_5.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_5.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L6=comp_comp_0.layers.add(comp_comp_1);
try { L6.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L6.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L6.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L6.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L6.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L6.property('ADBE Effect Parade');
var ef0_6=effPar.addProperty('ADBE Fill'); ef0_6.name='Fill';
try { ef0_6.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0002').setValue([0.99609375, 0.992156982422, 0.87451171875, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_6.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }

var L1_2=comp_comp_1.layers.addText('3D TEXT');
var textAnimators = L1_2.property('ADBE Text Animators');
if (textAnimators) {
  var ta = textAnimators.addProperty('ADBE Text Animator');
  if (ta) {
    try { ta.property('t').setValue(0); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('xe').setValue({'a': 0, 'k': 0, 'ix': 7}); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('ne').setValue({'a': 0, 'k': 0, 'ix': 8}); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('a').setValue({'a': 0, 'k': 100, 'ix': 4}); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('b').setValue(1); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('rn').setValue(0); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('sh').setValue(1); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('sm').setValue({'a': 0, 'k': 100, 'ix': 6}); } catch(e) { /* Property cannot be set: */ }
    try { ta.property('r').setValue(1); } catch(e) { /* Property cannot be set: */ }
  }
}
try { L1_2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L1_2.property('Transform').property('Scale').setValue([139, 139, 100]); } catch(e) { /* Property cannot be set: */ }
try { L1_2.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L1_2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L1_2.property('Transform').property('Anchor Point').setValue([-13.672, -109.375, 0]); } catch(e) { /* Property cannot be set: */ }

var L1_3=comp_comp_2.layers.add(comp_comp_1);
try { L1_3.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L1_3.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L1_3.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L1_3.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L1_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L1_3.property('ADBE Effect Parade');
var ef0_7=effPar.addProperty('ADBE Fill'); ef0_7.name='Fill';
try { ef0_7.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0002').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_7.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var ef1=effPar.addProperty('CC Radial Fast Blur'); ef1.name='CC Radial Fast Blur';
try { ef1.property('CC Radial Fast Blur-0001').setValue([1836, 4]); } catch(e) { /* Property cannot be set: */ }
try { ef1.property('CC Radial Fast Blur-0002').setValue(12.5); } catch(e) { /* Property cannot be set: */ }
try { ef1.property('CC Radial Fast Blur-0003').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L2_2=comp_comp_2.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 3',1920,1080,1);
try { L2_2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L2_2.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L2_2.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L2_2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L2_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
L2_2.trackMatteType=TrackMatteType.ALPHA;
var effPar=L2_2.property('ADBE Effect Parade');
var ef0_8=effPar.addProperty('ADBE Grid'); ef0_8.name='Grid';
try { ef0_8.property('ADBE Grid-0001').setValue([1798, 540]); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0002').setValue(3); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0003').setValue([1152, 648]); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0004').setValue(1581); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0005').setValue(18.4); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0006').setValue(2.3); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0008').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0009').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0010').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0011').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0012').setValue([0, 0, 0, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0013').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { ef0_8.property('ADBE Grid-0014').setValue(1); } catch(e) { /* Property cannot be set: */ }
var ef1_2=effPar.addProperty('ADBE Turbulent Displace'); ef1_2.name='Turbulent Displace';
try { ef1_2.property('ADBE Turbulent Displace-0001').setValue(1); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0002').setValue(85); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0003').setValue(153); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0004').setValue([960, 540]); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0008').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0009').setValue(1); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0010').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0011').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0012').setValue(3); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0013').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef1_2.property('ADBE Turbulent Displace-0014').setValue(1); } catch(e) { /* Property cannot be set: */ }
var L3_2=comp_comp_2.layers.add(comp_comp_1);
try { L3_2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L3_2.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L3_2.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L3_2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L3_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L3_2.property('ADBE Effect Parade');
var ef0_9=effPar.addProperty('ADBE Fill'); ef0_9.name='Fill';
try { ef0_9.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0002').setValue([0.207843154669, 0.368627458811, 0.541176497936, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_9.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var ef1_3=effPar.addProperty('CC Radial Fast Blur'); ef1_3.name='CC Radial Fast Blur';
try { ef1_3.property('CC Radial Fast Blur-0001').setValue([1836, 4]); } catch(e) { /* Property cannot be set: */ }
try { ef1_3.property('CC Radial Fast Blur-0002').setValue(12.5); } catch(e) { /* Property cannot be set: */ }
try { ef1_3.property('CC Radial Fast Blur-0003').setValue(1); } catch(e) { /* Property cannot be set: */ }

var L1_4=comp_comp_3.layers.add(comp_comp_1);
try { L1_4.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L1_4.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L1_4.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L1_4.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L1_4.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L1_4.property('ADBE Effect Parade');
var ef0_10=effPar.addProperty('ADBE Fill'); ef0_10.name='Fill';
try { ef0_10.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0002').setValue([0.980377197266, 0.494110107422, 0.101959228516, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_10.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }
var ef1_4=effPar.addProperty('CC Radial Fast Blur'); ef1_4.name='CC Radial Fast Blur';
try { ef1_4.property('CC Radial Fast Blur-0001').setValue([1836, 4]); } catch(e) { /* Property cannot be set: */ }
try { ef1_4.property('CC Radial Fast Blur-0002').setValue(39.5); } catch(e) { /* Property cannot be set: */ }
try { ef1_4.property('CC Radial Fast Blur-0003').setValue(1); } catch(e) { /* Property cannot be set: */ }

var L1_5=comp_root_2161978942400.layers.add(comp_comp_0);
try { L1_5.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L1_5.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L1_5.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L1_5.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L1_5.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var L2_3=comp_root_2161978942400.layers.add(comp_comp_2);
try { L2_3.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L2_3.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L2_3.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L2_3.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L2_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var L3_3=comp_root_2161978942400.layers.add(comp_comp_3);
try { L3_3.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L3_3.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L3_3.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L3_3.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L3_3.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var L4_2=comp_root_2161978942400.layers.add(comp_comp_3);
try { L4_2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L4_2.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L4_2.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L4_2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L4_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L4_2.property('ADBE Effect Parade');
var ef0_11=effPar.addProperty('ADBE Drop Shadow'); ef0_11.name='Drop Shadow';
try { ef0_11.property('ADBE Drop Shadow-0001').setValue([0, 0, 0, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_11.property('ADBE Drop Shadow-0002').setValue(160.2); } catch(e) { /* Property cannot be set: */ }
try { ef0_11.property('ADBE Drop Shadow-0003').setValue(225); } catch(e) { /* Property cannot be set: */ }
try { ef0_11.property('ADBE Drop Shadow-0004').setValue(36.4); } catch(e) { /* Property cannot be set: */ }
try { ef0_11.property('ADBE Drop Shadow-0005').setValue(59); } catch(e) { /* Property cannot be set: */ }
try { ef0_11.property('ADBE Drop Shadow-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
var L5_2=comp_root_2161978942400.layers.addSolid([0.4588235294117647,0.6745098039215687,0.615686274509804],'Medium Gray-Turquoise Solid 1',1920,1080,1);
try { L5_2.property('Transform').property('Position').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
try { L5_2.property('Transform').property('Scale').setValue([100, 100, 100]); } catch(e) { /* Property cannot be set: */ }
try { L5_2.property('Transform').property('Rotation').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { L5_2.property('Transform').property('Opacity').setValue(100); } catch(e) { /* Property cannot be set: */ }
try { L5_2.property('Transform').property('Anchor Point').setValue([960, 540, 0]); } catch(e) { /* Property cannot be set: */ }
var effPar=L5_2.property('ADBE Effect Parade');
var ef0_12=effPar.addProperty('ADBE Fill'); ef0_12.name='Fill';
try { ef0_12.property('ADBE Fill-0001').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0007').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0002').setValue([0.882354736328, 0.301971435547, 0.168640136719, 1]); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0006').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0003').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0004').setValue(0); } catch(e) { /* Property cannot be set: */ }
try { ef0_12.property('ADBE Fill-0005').setValue(1); } catch(e) { /* Property cannot be set: */ }

app.endUndoGroup();